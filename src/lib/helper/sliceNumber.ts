export const sliceNumber = (number: string | number) => {
  // Convert to number first to handle string inputs properly
  const num = typeof number === 'string' ? parseFloat(number) : number;

  // Check if it's a valid number
  if (isNaN(num)) return '0';

  // Format with proper decimal places
  let formatted;

  if (num >= 1000000) {
    // For large numbers, show 2 decimal places max
    formatted = num.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    });
  } else if (num >= 1) {
    // For numbers >= 1, show up to 4 decimal places
    formatted = num.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 4,
    });
  } else {
    // For small numbers < 1, show up to 8 decimal places
    formatted = num.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 8,
    });
  }

  return formatted;
};

// Helper function specifically for crypto amounts
export const formatCryptoAmount = (amount: string | number) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount;

  if (isNaN(num)) return '0';

  // Remove trailing zeros after decimal point
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 8,
  }).replace(/\.?0+$/, '');
};

// Helper function specifically for fiat amounts (Toman, USD)
export const formatFiatAmount = (amount: string | number) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount;

  if (isNaN(num)) return '0';

  return num.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
};
