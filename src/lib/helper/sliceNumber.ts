export const sliceNumber = (number: string | number) => {
  // Convert to number first to handle string inputs properly
  const num = typeof number === 'string' ? parseFloat(number) : number;

  // Check if it's a valid number
  if (isNaN(num)) return '0';

  // For integers, don't show decimal places
  if (Number.isInteger(num)) {
    return num.toLocaleString('en-US');
  }

  // For decimal numbers, show appropriate decimal places
  let maxDecimals = 8;

  if (num >= 1000000) {
    maxDecimals = 2; // Large numbers: max 2 decimals
  } else if (num >= 1) {
    maxDecimals = 6; // Medium numbers: max 6 decimals
  } else {
    maxDecimals = 8; // Small numbers: max 8 decimals
  }

  // Format and remove trailing zeros
  const formatted = num.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: maxDecimals,
  });

  return formatted;
};


