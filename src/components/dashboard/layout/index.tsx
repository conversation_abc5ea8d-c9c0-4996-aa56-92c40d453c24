"use client";
import Header from "./header";
import Sidebar from "./sidebar";
import BottomBar from "./BottomBar";
import { useRejectedStatus } from "@/hooks/useRejectedStatus";
import RejectedStatusModal from "@/components/dashboard/modals/RejectedStatusModal";

const Layout = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  const { isModalOpen, closeModal } = useRejectedStatus();

  return (
    <>
      <Header />
      <div className="flex gap-x-5 mt-5">
        <Sidebar />
        <main className="w-full lg:w-[calc(100%-244px)] pb-20 lg:pb-0">{children}</main>
      </div>

      {/* Bottom Bar for Mobile */}
      <BottomBar />

      {/* Rejected Status Modal */}
      <RejectedStatusModal isOpen={isModalOpen} onClose={closeModal} />
    </>
  );
};

export default Layout;
