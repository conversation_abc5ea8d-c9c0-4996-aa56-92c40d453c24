"use client";
import Header from "./header";
import Sidebar from "./sidebar";
import BottomBar from "./BottomBar";
import { useRejectedStatus } from "@/hooks/useRejectedStatus";
import RejectedStatusModal from "@/components/dashboard/modals/RejectedStatusModal";

const Layout = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  const { isModalOpen, closeModal } = useRejectedStatus();

  return (
    <div className="min-h-screen bg-[#0F1116]">
      {/* Header */}
      <div className="relative z-30">
        <Header />
      </div>

      {/* Sidebar - Fixed */}
      <Sidebar />

      {/* Main Content */}
      <main className="relative z-10 w-full lg:pr-[264px] pb-20 lg:pb-0 px-5 mt-5">
        {children}
      </main>

      {/* Bottom Bar for Mobile */}
      <BottomBar />

      {/* Rejected Status Modal */}
      <RejectedStatusModal isOpen={isModalOpen} onClose={closeModal} />
    </div>
  );
};

export default Layout;
