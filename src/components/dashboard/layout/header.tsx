"use client";
import Image from "next/image";
import Link from "next/link";
import { useState, useRef, useEffect } from "react";
import { RiMenu3Line } from "react-icons/ri";
import { getProfile, readNotif } from "@/requests/dashboardRequest";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import jalaali from "jalaali-js";
import { motion } from "framer-motion";
import { FaMoon, FaSun, FaLanguage, FaWallet, FaChartLine, FaBell, FaSearch, FaCreditCard, FaUser } from "react-icons/fa";
import { sliceNumber } from "@/lib/helper/sliceNumber";

interface IUserProfile {
  national_id?: string;
  firstname?: string;
  lastname?: string;
  birth_date?: string;
  phone?: string;
  alerts?: { id: number; message: string; created_at: string; read: number }[];
  tomanBalance?: number;
}
const Header: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState<boolean>(false);
  const [isUserModalOpen, setIsUserModalOpen] = useState<boolean>(false);
  const [isShowNotif, setIsShowNotif] = useState<boolean>(false);
  const [isThemeMenuOpen, setIsThemeMenuOpen] = useState<boolean>(false);
  const [isLanguageMenuOpen, setIsLanguageMenuOpen] = useState<boolean>(false);
  const [isQuickMenuOpen, setIsQuickMenuOpen] = useState<boolean>(false);
  const [isSearchActive, setIsSearchActive] = useState<boolean>(false);
  const [isDarkMode, setIsDarkMode] = useState<boolean>(true);
  const [currentLanguage, setCurrentLanguage] = useState<string>("fa");
  const [info, setInfo] = useState<IUserProfile>({});
  const [searchQuery, setSearchQuery] = useState<string>("");

  const modalRef = useRef<HTMLDivElement | null>(null);
  const notifModalRef = useRef<HTMLDivElement>(null);
  const themeMenuRef = useRef<HTMLDivElement>(null);
  const languageMenuRef = useRef<HTMLDivElement>(null);
  const quickMenuRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Close notification panel when clicking outside
      if (
        notifModalRef.current &&
        !notifModalRef.current.contains(event.target as Node)
      ) {
        setIsShowNotif(false);
      }

      // Close theme menu when clicking outside
      if (
        themeMenuRef.current &&
        !themeMenuRef.current.contains(event.target as Node)
      ) {
        setIsThemeMenuOpen(false);
      }

      // Close language menu when clicking outside
      if (
        languageMenuRef.current &&
        !languageMenuRef.current.contains(event.target as Node)
      ) {
        setIsLanguageMenuOpen(false);
      }

      // Close quick menu when clicking outside
      if (
        quickMenuRef.current &&
        !quickMenuRef.current.contains(event.target as Node)
      ) {
        setIsQuickMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  async function getProfileHandler() {
    const result = await getProfile();
    if (result.isError) {
      toast.error("خطایی رخ داد");
    } else {
      setInfo(result.data);
    }
  }

  useEffect(() => {
    getProfileHandler();
    function handleClickOutside(event: MouseEvent) {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        setIsUserModalOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("userPhone");
    document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    toast.success("با موفقیت از حساب کاربری خارج شدید");
    setIsUserModalOpen(false);
    router.push("/");
  };

  const readNotifHandler = async (id: number) => {
    const result = await readNotif(id);
    if (result.status === "success") {
      toast.success("عملیات با موفقیت انجام شد");
      getProfileHandler();
    }
  };

  const toPersianNumber = (num: string): string => {
    const persianDigits = ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"];
    return num
      .toString()
      .replace(/[0-9]/g, (match) => persianDigits[parseInt(match)]);
  };

  const convertToJalali = (dateString: string): string => {
    const date = new Date(dateString);
    const jalaliDate = jalaali.toJalaali(date);
    return toPersianNumber(
      `${jalaliDate.jy}/${jalaliDate.jm}/${jalaliDate.jd}`
    );
  };

  return (
    <motion.header
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="fixed top-5 left-5 right-5 z-50 flex items-center justify-between backdrop-blur-sm bg-gradient-to-r from-[#141416]/95 via-[#1E1F25]/95 to-[#141416]/95 rounded-2xl h-20 px-4 lg:px-8 py-2 lg:flex-row-reverse border border-gray-800/50 shadow-[0_0_15px_rgba(0,0,0,0.5)] lg:right"
    >
      <div className="flex items-center justify-center w-full lg:w-auto lg:justify-start gap-x-3 lg:gap-x-5 lg:flex-row-reverse">
        {/* User Profile Section */}
        <div className="relative">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Image
              className="w-10 lg:w-12 h-10 lg:h-12 cursor-pointer rounded-full border-2 border-blue-500/50 shadow-[0_0_10px_rgba(59,130,246,0.5)]"
              src="/images/avatar.png"
              height={1000}
              width={1000}
              alt="avatar"
              onClick={() => setIsUserModalOpen(!isUserModalOpen)}
            />
          </motion.div>

          {isUserModalOpen && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
              ref={modalRef}
              className="fixed md:absolute top-[calc(50%+180px)] left-1/2 md:top-14 md:left-0 transform -translate-x-1/2 -translate-y-1/2 md:translate-y-0 md:translate-x-0 w-72 bg-gradient-to-b from-[#1A1D24] to-[#23262F] rounded-xl border border-blue-500/30 shadow-[0_8px_32px_rgba(59,130,246,0.4)] z-[60] overflow-hidden backdrop-blur-lg"
            >
              {/* Glowing top border */}
              <div className="w-full h-[3px] bg-gradient-to-r from-blue-700 via-blue-400 to-blue-700 animate-pulse"></div>

              {/* Background decorative elements */}
              <div className="absolute top-0 left-0 w-full h-full overflow-hidden opacity-10 pointer-events-none">
                <div className="absolute -top-10 -right-10 w-40 h-40 rounded-full bg-blue-500 blur-3xl"></div>
                <div className="absolute -bottom-10 -left-10 w-40 h-40 rounded-full bg-indigo-500 blur-3xl"></div>
              </div>

              <div className="p-5 relative z-10">
                <div className="flex flex-col space-y-5">
                  {/* User profile section with enhanced styling */}
                  <div className="flex items-center justify-center mb-2">
                    <div className="flex flex-col items-center space-y-3">
                      {/* Avatar with animated glow effect */}
                      <div className="relative group">
                        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 blur-md opacity-70 group-hover:opacity-100 transition-opacity duration-300 animate-pulse"></div>
                        <motion.div
                          whileHover={{ scale: 1.08, rotate: 5 }}
                          className="relative"
                        >
                          <motion.img
                            initial={{ rotate: 0 }}
                            animate={{ rotate: [0, -5, 5, 0] }}
                            transition={{ duration: 5, repeat: Infinity, repeatType: "reverse", ease: "easeInOut" }}
                            src="/images/avatar.png"
                            alt="User"
                            className="relative w-20 h-20 rounded-full border-2 border-blue-400 shadow-[0px_0px_25px_rgba(59,130,246,0.6)] z-10"
                          />
                          {info.national_id && (
                            <div className="absolute -bottom-1 -right-1 bg-green-500 w-6 h-6 rounded-full flex items-center justify-center border-2 border-[#23262F] z-20">
                              <motion.div
                                initial={{ scale: 0.8 }}
                                animate={{ scale: [0.8, 1.1, 0.8] }}
                                transition={{ duration: 2, repeat: Infinity }}
                              >
                                ✓
                              </motion.div>
                            </div>
                          )}
                        </motion.div>
                      </div>

                      {/* User name with enhanced typography */}
                      <div className="text-center">
                        <h3 className="text-white text-lg font-bold bg-gradient-to-r from-blue-400 to-indigo-300 bg-clip-text text-transparent">
                          {info.firstname && info.lastname
                            ? `${info.firstname} ${info.lastname}`
                            : "کاربر مهمان"}
                        </h3>
                        <p className="text-gray-400 text-xs mt-1">
                          {info.phone || "شماره تلفن موجود نیست"}
                        </p>
                      </div>

                      {/* Balance display with enhanced styling */}
                      {info.tomanBalance !== undefined && (
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          className="flex items-center gap-2 bg-gradient-to-r from-[#1E2028] to-[#141416] px-4 py-2 rounded-full mt-1 border border-blue-500/20 shadow-[0_0_15px_rgba(59,130,246,0.2)]"
                        >
                          <FaWallet className="text-blue-400" />
                          <p className="text-blue-400 text-sm font-medium">
                            {toPersianNumber(sliceNumber(
                      Number(info.tomanBalance || 0).toFixed(0)
                    ))} تومان
                          </p>
                        </motion.div>
                      )}
                    </div>
                  </div>

                  {/* Authentication button with enhanced styling */}
                  <div className="flex justify-center">
                    <Link href="/dashboard/authenticate" className="w-full">
                      <motion.button
                        whileHover={{ scale: 1.03, boxShadow: "0px 0px 25px rgba(59,130,246,0.6)" }}
                        whileTap={{ scale: 0.97 }}
                        className="w-full border border-blue-500/30 bg-gradient-to-r from-blue-600/30 to-indigo-600/30 shadow-[0px_0px_20px_rgba(59,130,246,0.4),inset_0px_0px_20px_0px_rgba(59,130,246,0.3)] text-white px-4 py-3 rounded-lg cursor-pointer font-medium relative overflow-hidden group"
                      >
                        <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-500/10 to-indigo-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                        <span className="relative z-10 flex items-center justify-center gap-2">
                          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M20.5899 22C20.5899 18.13 16.7399 15 11.9999 15C7.25991 15 3.40991 18.13 3.40991 22" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                          احراز هویت
                        </span>
                      </motion.button>
                    </Link>
                  </div>

                  {/* Menu options with enhanced styling */}
                  <div className="flex flex-col space-y-3 pt-3 border-t border-blue-500/20">
                    <Link href="/dashboard/settings">
                      <motion.button
                        whileHover={{ scale: 1.03, x: 5, backgroundColor: "rgba(59, 130, 246, 0.1)" }}
                        className="w-full text-white cursor-pointer flex items-center justify-start gap-3 py-2 px-3 rounded-lg transition-all duration-300"
                      >
                        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500/20 to-indigo-500/20 flex items-center justify-center">
                          <svg className="w-4 h-4 text-blue-400" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M2 12.8799V11.1199C2 10.0799 2.85 9.21994 3.9 9.21994C5.71 9.21994 6.45 7.93994 5.54 6.36994C5.02 5.46994 5.33 4.29994 6.24 3.77994L7.97 2.78994C8.76 2.31994 9.78 2.59994 10.25 3.38994L10.36 3.57994C11.26 5.14994 12.74 5.14994 13.65 3.57994L13.76 3.38994C14.23 2.59994 15.25 2.31994 16.04 2.78994L17.77 3.77994C18.68 4.29994 18.99 5.46994 18.47 6.36994C17.56 7.93994 18.3 9.21994 20.11 9.21994C21.15 9.21994 22.01 10.0699 22.01 11.1199V12.8799C22.01 13.9199 21.16 14.7799 20.11 14.7799C18.3 14.7799 17.56 16.0599 18.47 17.6299C18.99 18.5399 18.68 19.6999 17.77 20.2199L16.04 21.2099C15.25 21.6799 14.23 21.3999 13.76 20.6099L13.65 20.4199C12.75 18.8499 11.27 18.8499 10.36 20.4199L10.25 20.6099C9.78 21.3999 8.76 21.6799 7.97 21.2099L6.24 20.2199C5.33 19.6999 5.02 18.5299 5.54 17.6299C6.45 16.0599 5.71 14.7799 3.9 14.7799C2.85 14.7799 2 13.9199 2 12.8799Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <span>تنظیمات</span>
                      </motion.button>
                    </Link>

                    <motion.button
                      whileHover={{ scale: 1.03, x: 5, backgroundColor: "rgba(239, 68, 68, 0.1)" }}
                      onClick={handleLogout}
                      className="w-full text-white cursor-pointer flex items-center justify-start gap-3 py-2 px-3 rounded-lg transition-all duration-300"
                    >
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-red-500/20 to-orange-500/20 flex items-center justify-center">
                        <svg className="w-4 h-4 text-red-400" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M8.90002 7.55999C9.21002 3.95999 11.06 2.48999 15.11 2.48999H15.24C19.71 2.48999 21.5 4.27999 21.5 8.74999V15.27C21.5 19.74 19.71 21.53 15.24 21.53H15.11C11.09 21.53 9.24002 20.08 8.91002 16.54" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M15 12H3.62" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M5.85 8.6499L2.5 11.9999L5.85 15.3499" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <span>خروج از حساب کاربری</span>
                    </motion.button>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>

        {/* Action Icons Section */}
        <div className="flex items-center justify-center gap-x-3 lg:gap-x-5 lg:order-last">
          {/* Notification Bell */}
          <div className="relative">
            <motion.div
              whileHover={{ scale: 1.1, boxShadow: "0 0 15px rgba(59, 130, 246, 0.3)" }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setIsShowNotif(!isShowNotif)}
              className={`cursor-pointer p-2 rounded-full transition-all duration-300 ${
                isShowNotif
                  ? "bg-gradient-to-r from-blue-600 to-blue-500 shadow-[0_0_15px_rgba(59,130,246,0.4)]"
                  : "bg-[#23262F]/80 hover:bg-[#2A2D38]"
              }`}
            >
              <FaBell className={`w-5 h-5 ${isShowNotif ? "text-white" : "text-gray-300 hover:text-white"} transition-colors`} />
              {info?.alerts?.length ? (
                <motion.span
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{
                    type: "spring",
                    stiffness: 500,
                    damping: 15
                  }}
                  className="absolute bg-gradient-to-r from-red-500 to-red-600 w-5 h-5 flex justify-center items-center rounded-full text-xs font-bold -top-1 -right-1 border border-gray-800 shadow-[0_0_10px_rgba(239,68,68,0.5)]"
                >
                  <motion.span
                    animate={{ scale: [0.9, 1.1, 0.9] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    {info?.alerts?.length}
                  </motion.span>
                </motion.span>
              ) : null}
            </motion.div>

            {isShowNotif && (
              <motion.div
                initial={{ opacity: 0, y: 10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.3, type: "spring", stiffness: 300, damping: 25 }}
                ref={notifModalRef}
                className="absolute -left-50 top-12 md:-left-20 md:top-12 bg-gradient-to-br from-[#18191d] to-[#1C1E24] max-h-[400px] overflow-y-auto w-[280px] md:w-[350px] border border-gray-700/50 rounded-xl z-[60] shadow-[0_10px_40px_rgba(0,0,0,0.5)] backdrop-blur-sm"
              >
                {/* Decorative top gradient line */}
                <div
                  className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(72,153,235,0.5)]"
                  style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(72,153,235,1) 50%, rgba(211,211,211,0.1) 100%)' }}
                />

                <div className="sticky top-0 bg-gradient-to-r from-[#23262F] to-[#1C1E24] p-3 border-b border-gray-700/50 flex justify-between items-center backdrop-blur-sm z-10">
                  <div className="flex items-center gap-2">
                    <FaBell className="text-blue-400 w-4 h-4" />
                    <h3 className="text-white font-medium">اعلان‌ها</h3>
                  </div>
                  <motion.div
                    initial={{ scale: 0.5, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="flex items-center gap-1 bg-blue-500/10 px-2 py-1 rounded-full"
                  >
                    <span className="text-xs text-blue-400 font-medium">{info?.alerts?.length || 0}</span>
                    <span className="text-xs text-gray-400">اعلان</span>
                  </motion.div>
                </div>

                {info?.alerts?.length ? (
                  <div className="relative">
                    {/* Subtle pattern background */}
                    <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-10 pointer-events-none"></div>

                    {info.alerts.map((item, index) => (
                      <motion.div
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.05, duration: 0.3 }}
                        key={item.id}
                        className={`relative border-b border-gray-800/50 p-4 transition-all duration-300 ${
                          item.read === 0
                            ? "hover:bg-gradient-to-r hover:from-blue-600/10 hover:to-blue-500/5"
                            : "hover:bg-[#23262F]/30"
                        }`}
                        whileHover={{ y: -2 }}
                      >
                        {/* Unread indicator line */}
                        {item.read === 0 && (
                          <motion.div
                            className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500 to-blue-600"
                            layoutId={`indicator-${item.id}`}
                            initial={{ height: 0 }}
                            animate={{ height: '100%' }}
                            transition={{ duration: 0.3, delay: 0.1 }}
                          />
                        )}

                        <div className="flex justify-between items-center mb-2">
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-gray-400 bg-[#23262F]/80 px-2 py-1 rounded-full">
                              {convertToJalali(item.created_at)}
                            </span>
                          </div>
                          {item.read === 0 && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ type: "spring", stiffness: 500, delay: index * 0.05 + 0.2 }}
                              className="w-3 h-3 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full shadow-[0_0_10px_rgba(59,130,246,0.5)]"
                            />
                          )}
                        </div>

                        <div className="flex items-start gap-x-3">
                          <div className="flex-1">
                            <p className={`text-sm ${item.read === 0 ? 'text-white' : 'text-gray-400'} transition-colors duration-300`}>
                              {item.message}
                            </p>
                          </div>

                          {item.read === 0 && (
                            <motion.button
                              whileHover={{ scale: 1.05, boxShadow: "0 0 15px rgba(59, 130, 246, 0.5)" }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => readNotifHandler(item.id)}
                              type="button"
                              className="text-white bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 rounded-lg text-xs px-3 py-1.5 cursor-pointer shadow-lg transition-all duration-300 flex items-center gap-1"
                            >
                              <span>خواندن</span>
                              <motion.div
                                animate={{ x: [0, 5, 0] }}
                                transition={{ duration: 1.5, repeat: Infinity, repeatType: "loop" }}
                                className="w-2 h-2 bg-white rounded-full opacity-70"
                              />
                            </motion.button>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="flex flex-col items-center justify-center py-12 text-center"
                  >
                    <div className="bg-[#23262F] p-4 rounded-full mb-4">
                      <FaBell className="w-8 h-8 text-gray-500" />
                    </div>
                    <h3 className="text-lg font-medium text-white mb-2">اعلانی وجود ندارد</h3>
                    <p className="text-gray-400 text-sm max-w-[250px]">
                      اعلان‌های مهم و به‌روزرسانی‌های سیستم در اینجا نمایش داده می‌شوند.
                    </p>
                  </motion.div>
                )}

                {(info?.alerts?.length || 0) > 5 && (
                  <div className="sticky bottom-0 bg-gradient-to-t from-[#18191d] to-transparent h-10 w-full pointer-events-none" />
                )}
              </motion.div>
            )}
          </div>


          {/* Quick Access Menu */}
          <div className="relative">
            <motion.div
              whileHover={{ scale: 1.1, boxShadow: "0 0 15px rgba(59, 130, 246, 0.3)" }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setIsQuickMenuOpen(!isQuickMenuOpen)}
              className={`cursor-pointer p-2 rounded-full transition-all duration-300 ${
                isQuickMenuOpen
                  ? "bg-gradient-to-r from-blue-600 to-blue-500 shadow-[0_0_15px_rgba(59,130,246,0.4)]"
                  : "bg-[#23262F]/80 hover:bg-[#2A2D38]"
              }`}
            >
              <FaChartLine className={`w-5 h-5 ${isQuickMenuOpen ? "text-white" : "text-gray-300 hover:text-white"} transition-colors`} />
            </motion.div>

            {isQuickMenuOpen && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
                ref={quickMenuRef}
                className="absolute -left-20 top-12 bg-[#23262F] w-56 border border-gray-700 rounded-lg z-[60] shadow-[0_5px_25px_rgba(0,0,0,0.3)] overflow-hidden"
              >
                <div className="p-2 border-b border-gray-700">
                  <h3 className="text-white font-medium text-sm">دسترسی سریع</h3>
                </div>
                <div className="p-2 grid grid-cols-2 gap-2">
                  <Link href="/dashboard/deposit">
                    <motion.div
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.2)" }}
                      className="flex flex-col items-center gap-1 p-2 rounded-md text-gray-300 hover:text-blue-400"
                    >
                      <FaWallet size={18} />
                      <span className="text-xs">واریز</span>
                    </motion.div>
                  </Link>
                  <Link href="/dashboard/withdraw">
                    <motion.div
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.2)" }}
                      className="flex flex-col items-center gap-1 p-2 rounded-md text-gray-300 hover:text-blue-400"
                    >
                      <FaWallet size={18} />
                      <span className="text-xs">برداشت</span>
                    </motion.div>
                  </Link>
                  <Link href="/dashboard/card-managment">
                    <motion.div
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.2)" }}
                      className="flex flex-col items-center gap-1 p-2 rounded-md text-gray-300 hover:text-blue-400"
                    >
                      <FaCreditCard size={18} />
                      <span className="text-xs">کارت‌های بانکی</span>
                    </motion.div>
                  </Link>
                  <Link href="/dashboard/price">
                    <motion.div
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.2)" }}
                      className="flex flex-col items-center gap-1 p-2 rounded-md text-gray-300 hover:text-blue-400"
                    >
                      <FaChartLine size={18} />
                      <span className="text-xs">قیمت‌ها</span>
                    </motion.div>
                  </Link>
                  <Link href="/dashboard/profile">
                    <motion.div
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.2)" }}
                      className="flex flex-col items-center gap-1 p-2 rounded-md text-gray-300 hover:text-blue-400"
                    >
                      <FaUser size={18} />
                      <span className="text-xs">پروفایل</span>
                    </motion.div>
                  </Link>
                </div>
              </motion.div>
            )}
          </div>

        </div>
      </div>

      {/* Desktop Navigation */}
      <nav className="hidden lg:flex items-center gap-x-4 ml-auto lg:flex-row-reverse">
        <Link href="/support" className="text-lg font-light">
          تماس با ما
        </Link>
        <Link href="/blog" className="text-lg font-light">
          وبلاگ
        </Link>
        <Link href="/dashboard/price" className="text-lg font-light">
          قیمت لحظه ای
        </Link>
        <Link href="/" className="text-lg font-light">
          خرید و فروش رمز ارز
        </Link>
        <Image
          className="w-40 h-15 mx-auto"
          src="/images/main-logo.png"
          height={1000}
          width={1000}
          alt="logo"
        />
      </nav>

      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        className="lg:hidden absolute left-5 p-2 rounded-lg bg-[#18191D] shadow-[0_0_10px_rgba(192,192,192,0.3)]"
      >
        <RiMenu3Line
          size={24}
          color="white"
          className="transform scale-x-[-1]"
        />
      </button>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-[55] lg:hidden h-screen"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

  
    </motion.header>
  );
};

export default Header;
