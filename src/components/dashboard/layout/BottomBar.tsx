"use client";
import { useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { 
  FaHome, 
  FaWallet, 
  FaHistory, 
  FaUser, 
  FaPlus,
  FaChevronUp,
  FaChevronDown
} from "react-icons/fa";

interface BottomBarItem {
  id: string;
  label: string;
  icon: string;
  href?: string;
  hasSubmenu?: boolean;
  submenu?: {
    label: string;
    href: string;
    icon?: string;
  }[];
}

const BottomBar = () => {
  const pathname = usePathname();
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);

  const bottomBarItems: BottomBarItem[] = [
    {
      id: "home",
      label: "خانه",
      icon: "/images/home.svg",
      href: "/dashboard"
    },
    {
      id: "wallet",
      label: "کیف پول",
      icon: "/images/asset.svg",
      hasSubmenu: true,
      submenu: [
        { label: "واریز", href: "/dashboard/deposit", icon: "/images/deposit.png" },
        { label: "برداشت", href: "/dashboard/withdraw", icon: "/images/send.svg" },
        { label: "کارت‌ها", href: "/dashboard/card-managment", icon: "/images/credit-card.svg" }
      ]
    },
    {
      id: "history",
      label: "سوابق",
      icon: "/images/history.svg",
      hasSubmenu: true,
      submenu: [
        { label: "واریز", href: "/dashboard/history/deposit" },
        { label: "برداشت", href: "/dashboard/history/withdraw" },
        { label: "سفارش", href: "/dashboard/history/order" }
      ]
    },
    {
      id: "auth",
      label: "احراز",
      icon: "/images/auth.svg",
      href: "/dashboard/authenticate"
    },
    {
      id: "profile",
      label: "پروفایل",
      icon: "/images/setting.svg",
      hasSubmenu: true,
      submenu: [
        { label: "پیام‌ها", href: "/dashboard/profile/messages", icon: "/images/mail.svg" },
        { label: "تنظیمات", href: "/dashboard/settings", icon: "/images/setting.svg" },
        { label: "تیکت", href: "/dashboard/tickets", icon: "/images/mail.svg" },
        { label: "کسب درآمد", href: "/dashboard/referral", icon: "/images/star.png" }
      ]
    }
  ];

  const isItemActive = (item: BottomBarItem) => {
    if (item.href) {
      return item.href === "/dashboard" 
        ? pathname === "/dashboard"
        : pathname.startsWith(item.href);
    }
    
    if (item.submenu) {
      return item.submenu.some(subItem => pathname.startsWith(subItem.href));
    }
    
    return false;
  };

  const handleItemClick = (item: BottomBarItem) => {
    if (item.hasSubmenu) {
      setActiveSubmenu(activeSubmenu === item.id ? null : item.id);
    } else {
      setActiveSubmenu(null);
    }
  };

  const handleSubmenuClick = () => {
    setActiveSubmenu(null);
  };

  return (
    <>
      {/* Backdrop for submenu */}
      <AnimatePresence>
        {activeSubmenu && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setActiveSubmenu(null)}
          />
        )}
      </AnimatePresence>

      {/* Submenu Panel */}
      <AnimatePresence>
        {activeSubmenu && (
          <motion.div
            initial={{ y: "100%" }}
            animate={{ y: 0 }}
            exit={{ y: "100%" }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="fixed bottom-20 left-4 right-4 bg-gradient-to-br from-[#23262F] to-[#1C1E24] rounded-2xl shadow-2xl z-50 lg:hidden border border-gray-800/50 backdrop-blur-xl"
          >
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-white font-medium">
                  {bottomBarItems.find(item => item.id === activeSubmenu)?.label}
                </h3>
                <button
                  onClick={() => setActiveSubmenu(null)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <FaChevronDown size={16} />
                </button>
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                {bottomBarItems
                  .find(item => item.id === activeSubmenu)
                  ?.submenu?.map((subItem, index) => (
                    <Link
                      key={subItem.href}
                      href={subItem.href}
                      onClick={handleSubmenuClick}
                      className="group"
                    >
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className={`flex flex-col items-center p-3 rounded-xl transition-all duration-300 ${
                          pathname.startsWith(subItem.href)
                            ? "bg-blue-500/20 border border-blue-500/30"
                            : "bg-gray-800/30 hover:bg-gray-700/50 border border-transparent"
                        }`}
                      >
                        {subItem.icon && (
                          <Image
                            src={subItem.icon}
                            alt={subItem.label}
                            width={20}
                            height={20}
                            className="mb-2 opacity-80 group-hover:opacity-100 transition-opacity"
                          />
                        )}
                        <span className={`text-xs font-medium ${
                          pathname.startsWith(subItem.href)
                            ? "text-blue-400"
                            : "text-gray-300 group-hover:text-white"
                        }`}>
                          {subItem.label}
                        </span>
                      </motion.div>
                    </Link>
                  ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Bottom Bar */}
      <motion.div
        initial={{ y: 100 }}
        animate={{ y: 0 }}
        transition={{ type: "spring", damping: 25, stiffness: 300 }}
        className="fixed bottom-0 left-0 right-0 bg-gradient-to-t from-[#18191D] via-[#1E1F25] to-[#18191D]/95 backdrop-blur-xl border-t border-gray-800/50 z-30 lg:hidden"
      >
        <div className="flex items-center justify-around px-2 py-3 safe-area-bottom">
          {bottomBarItems.map((item) => {
            const isActive = isItemActive(item);
            const hasActiveSubmenu = activeSubmenu === item.id;
            
            return (
              <motion.div
                key={item.id}
                whileTap={{ scale: 0.95 }}
                className="relative"
              >
                {item.href ? (
                  <Link
                    href={item.href}
                    onClick={() => handleItemClick(item)}
                    className="flex flex-col items-center p-2 min-w-[60px]"
                  >
                    <div className={`relative p-2 rounded-xl transition-all duration-300 ${
                      isActive 
                        ? "bg-blue-500/20 shadow-[0_0_20px_rgba(59,130,246,0.3)]" 
                        : "hover:bg-gray-800/50"
                    }`}>
                      <Image
                        src={item.icon}
                        alt={item.label}
                        width={20}
                        height={20}
                        className={`transition-all duration-300 ${
                          isActive ? "brightness-125" : "opacity-70"
                        }`}
                      />
                      {isActive && (
                        <motion.div
                          layoutId="activeIndicator"
                          className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full"
                          transition={{ type: "spring", damping: 25, stiffness: 300 }}
                        />
                      )}
                    </div>
                    <span className={`text-xs mt-1 font-medium transition-colors duration-300 ${
                      isActive ? "text-blue-400" : "text-gray-400"
                    }`}>
                      {item.label}
                    </span>
                  </Link>
                ) : (
                  <button
                    onClick={() => handleItemClick(item)}
                    className="flex flex-col items-center p-2 min-w-[60px]"
                  >
                    <div className={`relative p-2 rounded-xl transition-all duration-300 ${
                      isActive || hasActiveSubmenu
                        ? "bg-blue-500/20 shadow-[0_0_20px_rgba(59,130,246,0.3)]" 
                        : "hover:bg-gray-800/50"
                    }`}>
                      <Image
                        src={item.icon}
                        alt={item.label}
                        width={20}
                        height={20}
                        className={`transition-all duration-300 ${
                          isActive || hasActiveSubmenu ? "brightness-125" : "opacity-70"
                        }`}
                      />
                      {item.hasSubmenu && (
                        <motion.div
                          animate={{ rotate: hasActiveSubmenu ? 180 : 0 }}
                          transition={{ duration: 0.3 }}
                          className="absolute -top-1 -right-1"
                        >
                          <FaChevronUp size={8} className="text-gray-400" />
                        </motion.div>
                      )}
                      {(isActive || hasActiveSubmenu) && (
                        <motion.div
                          layoutId="activeIndicator"
                          className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full"
                          transition={{ type: "spring", damping: 25, stiffness: 300 }}
                        />
                      )}
                    </div>
                    <span className={`text-xs mt-1 font-medium transition-colors duration-300 ${
                      isActive || hasActiveSubmenu ? "text-blue-400" : "text-gray-400"
                    }`}>
                      {item.label}
                    </span>
                  </button>
                )}
              </motion.div>
            );
          })}
        </div>
      </motion.div>
    </>
  );
};

export default BottomBar;
