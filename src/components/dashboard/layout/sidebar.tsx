"use client";
import { getProfile } from "@/requests/dashboardRequest";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";
import toast from "react-hot-toast";

import { usePathname } from "next/navigation";
import { motion } from "framer-motion";

interface IUserProfile {
  national_id?: string;
  firstname?: string;
  lastname?: string;
  birth_date?: string;
}

const Sidebar = () => {
  const pathname = usePathname();
  const [info, setInfo] = useState<IUserProfile>({});
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [isWalletOpen, setIsWalletOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);


  useEffect(() => {
    getProfileHandler();

    // Reset all accordion states first
    setIsHistoryOpen(false);
    setIsWalletOpen(false);
    setIsProfileOpen(false);

    // Then open the appropriate one based on the current path
    if (pathname.startsWith("/dashboard/history")) {
      setIsHistoryOpen(true);
    }
    if (
      pathname.startsWith("/dashboard/deposit") ||
      pathname.startsWith("/dashboard/withdraw") ||
      pathname.startsWith("/dashboard/assets") ||
      pathname.startsWith("/dashboard/card-managment")
    ) {
      setIsWalletOpen(true);
    }
    if (
      pathname.startsWith("/dashboard/profile") ||
      pathname.startsWith("/dashboard/settings")
    ) {
      setIsProfileOpen(true);
    }
  }, [pathname]);

  async function getProfileHandler() {
    const result = await getProfile();
    if (result.isError) {
      toast.error("خطایی رخ داد");
    } else {
      setInfo(result.data);
    }
  }

  // Modified toggle function to close other menus when one is opened
  const toggleMenu = (menuName: string) => {
    if (menuName === "history") {
      setIsHistoryOpen(!isHistoryOpen);
      setIsWalletOpen(false);
      setIsProfileOpen(false);
    } else if (menuName === "wallet") {
      setIsWalletOpen(!isWalletOpen);
      setIsHistoryOpen(false);
      setIsProfileOpen(false);
    } else if (menuName === "profile") {
      setIsProfileOpen(!isProfileOpen);
      setIsHistoryOpen(false);
      setIsWalletOpen(false);
    }
  };

  const handleLinkClick = () => {
    // No longer needed for mobile menu
  };

  const isLinkActive = (href: string) => {
    return href === "/dashboard"
      ? pathname === "/dashboard"
      : pathname.startsWith(href);
  };

  const getLinkClass = (href: string) => {
    return `flex items-center gap-x-4 h-14 px-3 ${
      isLinkActive(href) ? "border-r-8 border-[#E6E7E8] rounded-r-lg" : ""
    }`;
  };

  const isSubmenuLinkActive = (href: string) => {
    return pathname === href;
  };

  const getSubmenuLinkClass = (href: string) => {
    return `flex items-center gap-x-4 h-12 px-3 text-sm ${
      isSubmenuLinkActive(href)
        ? "border-r-8 border-[#E6E7E8] rounded-r-lg text-white"
        : "text-[#B1B5C3]"
    }`;
  };

  const isInSection = (paths: string[]) => {
    return paths.some((path) => pathname.startsWith(path));
  };



  return (
    <>



      <motion.div
        initial={{ x: 100, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ type: "spring", damping: 25, stiffness: 300, delay: 0.2 }}
        className="hidden lg:block w-[244px] fixed top-5 right-5 h-[calc(100vh-40px)] z-40"
      >
        {/* Glassmorphism Container */}
        <motion.div
          className="relative w-full h-full bg-gradient-to-br from-[#18191D]/80 via-[#1E1F25]/85 to-[#18191D]/80 backdrop-blur-xl border border-gray-700/30 rounded-2xl shadow-[0_20px_60px_rgba(0,0,0,0.4),0_0_40px_rgba(59,130,246,0.05)] overflow-hidden"
          whileHover={{
            boxShadow: "0 25px 80px rgba(0,0,0,0.5), 0 0 60px rgba(59,130,246,0.1)",
            borderColor: "rgba(59,130,246,0.3)"
          }}
          transition={{ duration: 0.3 }}
        >
          {/* Top Glow */}
          <div className="absolute top-0 left-1/2 -translate-x-1/2 w-32 h-[2px] bg-gradient-to-r from-transparent via-blue-500/60 to-transparent shadow-[0_0_20px_rgba(59,130,246,0.4)]" />

          {/* Floating Particles */}
          <motion.div
            className="absolute top-6 left-6 w-1 h-1 bg-blue-400/60 rounded-full"
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.6, 1, 0.6],
              x: [0, 10, 0]
            }}
            transition={{ duration: 3, repeat: Infinity }}
          />
          <motion.div
            className="absolute top-12 right-8 w-1 h-1 bg-purple-400/40 rounded-full"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.4, 0.8, 0.4],
              y: [0, -8, 0]
            }}
            transition={{ duration: 2.5, repeat: Infinity, delay: 1 }}
          />
          <motion.div
            className="absolute top-20 left-1/3 w-0.5 h-0.5 bg-cyan-400/50 rounded-full"
            animate={{
              scale: [1, 2, 1],
              opacity: [0.5, 1, 0.5],
              rotate: [0, 180, 360]
            }}
            transition={{ duration: 4, repeat: Infinity, delay: 2 }}
          />

          {/* Decorative Grid Pattern */}
          <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-10" />

          {/* Content Container */}
          <div className="relative z-10 px-4 py-6 h-full overflow-y-auto sidebar-scrollbar">
        <div className="flex flex-col h-full">
          <div className="flex items-center gap-x-2 border-b border-[#353945] pb-4">
            <Image
              className="w-14 h-14"
              src="/images/avatar.png"
              height={1000}
              width={1000}
              alt="avatar"
            />
            <div>
              <div className="flex items-center">
                <Image
                  className="w-4 h-4"
                  src="/images/waving.png"
                  height={1000}
                  width={1000}
                  alt="avatar"
                />
                <p className="text-xs text-[#B1B5C3] font-light">سلام</p>
              </div>
              <p className="text-sm font-medium">
                {info.firstname} {info.lastname}
              </p>
            </div>
          </div>
          <div className="py-5 flex-1">
            <motion.div
              whileHover={{
                scale: 1.02,
                x: 5,
                boxShadow: "0 5px 20px rgba(59,130,246,0.2)"
              }}
              whileTap={{ scale: 0.98 }}
              transition={{ duration: 0.2 }}
            >
              <Link
                onClick={handleLinkClick}
                className={`${getLinkClass("/dashboard")} transition-all duration-300 hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/5 rounded-lg`}
                href="/dashboard"
              >
                <motion.div
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.6 }}
                >
                  <Image
                    className="w-5 h-5"
                    src="/images/home.svg"
                    height={1000}
                    width={1000}
                    alt="avatar"
                  />
                </motion.div>
                <span className="transition-colors duration-300">ناحیه کاربری</span>
              </Link>
            </motion.div>
            <div>
              <button
                onClick={() => toggleMenu("wallet")}
                className={`flex items-center gap-x-4 h-14 px-3 w-full text-left cursor-pointer ${
                  isInSection([
                    "/dashboard/deposit",
                    "/dashboard/withdraw",
                    "/dashboard/assets",
                    "/dashboard/card-managment",
                  ])
                    ? "border-r-8 border-[#E6E7E8] rounded-r-lg"
                    : ""
                }`}
              >
                <Image
                  className="w-5 h-5"
                  src="/images/asset.svg"
                  height={1000}
                  width={1000}
                  alt="avatar"
                />
                <span className="whitespace-nowrap">کیف پول های من</span>
                <Image
                  className={`w-4 h-4 ml-auto transform transition-transform duration-300 ${
                    isWalletOpen ? "rotate-180" : ""
                  }`}
                  src="/images/arrow-bottom.svg"
                  height={1000}
                  width={1000}
                  alt="arrow"
                />
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  isWalletOpen ? "max-h-60" : "max-h-0"
                }`}
              >
                <div className="pl-8">
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass("/dashboard/deposit")}
                    href="/dashboard/deposit"
                  >
                    <span>واریز ها</span>
                  </Link>
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass("/dashboard/withdraw")}
                    href="/dashboard/withdraw"
                  >
                    <span>برداشت ها</span>
                  </Link>
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass("/dashboard/card-managment")}
                    href="/dashboard/card-managment"
                  >
                    <span>کارت‌های بانکی</span>
                  </Link>
                </div>
              </div>
            </div>
            <div>
              <button
                onClick={() => toggleMenu("history")}
                className={`flex items-center gap-x-4 h-14 px-3 w-full text-left cursor-pointer ${
                  isInSection(["/dashboard/history"])
                    ? "border-r-8 border-[#E6E7E8] rounded-r-lg"
                    : ""
                }`}
              >
                <Image
                  className="w-5 h-5"
                  src="/images/history.svg"
                  height={1000}
                  width={1000}
                  alt="avatar"
                />
                <span>سوابق</span>
                <Image
                  className={`w-4 h-4 ml-auto transform transition-transform duration-300 ${
                    isHistoryOpen ? "rotate-180" : ""
                  }`}
                  src="/images/arrow-bottom.svg"
                  height={1000}
                  width={1000}
                  alt="arrow"
                />
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  isHistoryOpen ? "max-h-60" : "max-h-0"
                }`}
              >
                <div className="pl-8">
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass(
                      "/dashboard/history/deposit"
                    )}
                    href="/dashboard/history/deposit"
                  >
                    <span>واریز</span>
                  </Link>
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass(
                      "/dashboard/history/withdraw"
                    )}
                    href="/dashboard/history/withdraw"
                  >
                    <span>برداشت</span>
                  </Link>
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass("/dashboard/history/order")}
                    href="/dashboard/history/order"
                  >
                    <span>سفارش</span>
                  </Link>
                </div>
              </div>
            </div>
            <Link
              onClick={handleLinkClick}
              className={getLinkClass("/dashboard/authenticate")}
              href="/dashboard/authenticate"
            >
              <Image
                className="w-5 h-5"
                src="/images/auth.svg"
                height={1000}
                width={1000}
                alt="avatar"
              />
              <span>احراز هویت</span>
            </Link>
            <Link
              onClick={handleLinkClick}
              className={getLinkClass("/dashboard/referral")}
              href="/dashboard/referral"
            >
              <Image
                className="w-5 h-5"
                src="/images/star.png"
                height={1000}
                width={1000}
                alt="referral"
              />
              <span>کسب درآمد</span>
            </Link>
            <Link
              onClick={handleLinkClick}
              className={getLinkClass("/dashboard/tickets")}
              href="/dashboard/tickets"
            >
              <Image
                className="w-5 h-5"
                src="/images/mail.svg"
                height={1000}
                width={1000}
                alt="avatar"
              />
              <span>تیکت پشتیبانی</span>
            </Link>
            <div>
              <button
                onClick={() => toggleMenu("profile")}
                className={`flex items-center gap-x-4 h-14 px-3 w-full text-left cursor-pointer ${
                  isInSection(["/dashboard/profile", "/dashboard/settings"])
                    ? "border-r-8 border-[#E6E7E8] rounded-r-lg"
                    : ""
                }`}
              >
                <Image
                  className="w-5 h-5"
                  src="/images/setting.svg"
                  height={1000}
                  width={1000}
                  alt="avatar"
                />
                <span>پروفایل</span>
                <Image
                  className={`w-4 h-4 ml-auto transform transition-transform duration-300 ${
                    isProfileOpen ? "rotate-180" : ""
                  }`}
                  src="/images/arrow-bottom.svg"
                  height={1000}
                  width={1000}
                  alt="arrow"
                />
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  isProfileOpen ? "max-h-60" : "max-h-0"
                }`}
              >
                <div className="pl-8">
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass(
                      "/dashboard/profile/messages"
                    )}
                    href="/dashboard/profile/messages"
                  >
                    <span>پیام ها</span>
                  </Link>
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass("/dashboard/settings")}
                    href="/dashboard/settings"
                  >
                    <span>تنظیمات</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
          </div>
          </div>
        </motion.div>
      </motion.div>
    </>
  );
};

export default Sidebar;
