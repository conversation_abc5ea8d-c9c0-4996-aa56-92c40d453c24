"use client";
import { getProfile } from "@/requests/dashboardRequest";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";
import toast from "react-hot-toast";

import { usePathname } from "next/navigation";

interface IUserProfile {
  national_id?: string;
  firstname?: string;
  lastname?: string;
  birth_date?: string;
}

const Sidebar = () => {
  const pathname = usePathname();
  const [info, setInfo] = useState<IUserProfile>({});
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [isWalletOpen, setIsWalletOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);


  useEffect(() => {
    getProfileHandler();

    // Reset all accordion states first
    setIsHistoryOpen(false);
    setIsWalletOpen(false);
    setIsProfileOpen(false);

    // Then open the appropriate one based on the current path
    if (pathname.startsWith("/dashboard/history")) {
      setIsHistoryOpen(true);
    }
    if (
      pathname.startsWith("/dashboard/deposit") ||
      pathname.startsWith("/dashboard/withdraw") ||
      pathname.startsWith("/dashboard/assets") ||
      pathname.startsWith("/dashboard/card-managment")
    ) {
      setIsWalletOpen(true);
    }
    if (
      pathname.startsWith("/dashboard/profile") ||
      pathname.startsWith("/dashboard/settings")
    ) {
      setIsProfileOpen(true);
    }
  }, [pathname]);

  async function getProfileHandler() {
    const result = await getProfile();
    if (result.isError) {
      toast.error("خطایی رخ داد");
    } else {
      setInfo(result.data);
    }
  }

  // Modified toggle function to close other menus when one is opened
  const toggleMenu = (menuName: string) => {
    if (menuName === "history") {
      setIsHistoryOpen(!isHistoryOpen);
      setIsWalletOpen(false);
      setIsProfileOpen(false);
    } else if (menuName === "wallet") {
      setIsWalletOpen(!isWalletOpen);
      setIsHistoryOpen(false);
      setIsProfileOpen(false);
    } else if (menuName === "profile") {
      setIsProfileOpen(!isProfileOpen);
      setIsHistoryOpen(false);
      setIsWalletOpen(false);
    }
  };

  const handleLinkClick = () => {
    // No longer needed for mobile menu
  };

  const isLinkActive = (href: string) => {
    return href === "/dashboard"
      ? pathname === "/dashboard"
      : pathname.startsWith(href);
  };

  const getLinkClass = (href: string) => {
    return `flex items-center gap-x-4 h-14 px-3 ${
      isLinkActive(href) ? "border-r-8 border-[#E6E7E8] rounded-r-lg" : ""
    }`;
  };

  const isSubmenuLinkActive = (href: string) => {
    return pathname === href;
  };

  const getSubmenuLinkClass = (href: string) => {
    return `flex items-center gap-x-4 h-12 px-3 text-sm ${
      isSubmenuLinkActive(href)
        ? "border-r-8 border-[#E6E7E8] rounded-r-lg text-white"
        : "text-[#B1B5C3]"
    }`;
  };

  const isInSection = (paths: string[]) => {
    return paths.some((path) => pathname.startsWith(path));
  };



  return (
    <>



      <div className="hidden lg:block w-[244px] bg-[#18191D] rounded-2xl px-4 h-screen overflow-y-auto py-12">
        <div className="flex flex-col h-full">
          <div className="flex items-center gap-x-2 border-b border-[#353945] pb-4">
            <Image
              className="w-14 h-14"
              src="/images/avatar.png"
              height={1000}
              width={1000}
              alt="avatar"
            />
            <div>
              <div className="flex items-center">
                <Image
                  className="w-4 h-4"
                  src="/images/waving.png"
                  height={1000}
                  width={1000}
                  alt="avatar"
                />
                <p className="text-xs text-[#B1B5C3] font-light">سلام</p>
              </div>
              <p className="text-sm font-medium">
                {info.firstname} {info.lastname}
              </p>
            </div>
          </div>
          <div className="py-5 flex-1">
            <Link
              onClick={handleLinkClick}
              className={getLinkClass("/dashboard")}
              href="/dashboard"
            >
              <Image
                className="w-5 h-5"
                src="/images/home.svg"
                height={1000}
                width={1000}
                alt="avatar"
              />
              <span>ناحیه کاربری</span>
            </Link>
            <div>
              <button
                onClick={() => toggleMenu("wallet")}
                className={`flex items-center gap-x-4 h-14 px-3 w-full text-left cursor-pointer ${
                  isInSection([
                    "/dashboard/deposit",
                    "/dashboard/withdraw",
                    "/dashboard/assets",
                    "/dashboard/card-managment",
                  ])
                    ? "border-r-8 border-[#E6E7E8] rounded-r-lg"
                    : ""
                }`}
              >
                <Image
                  className="w-5 h-5"
                  src="/images/asset.svg"
                  height={1000}
                  width={1000}
                  alt="avatar"
                />
                <span className="whitespace-nowrap">کیف پول های من</span>
                <Image
                  className={`w-4 h-4 ml-auto transform transition-transform duration-300 ${
                    isWalletOpen ? "rotate-180" : ""
                  }`}
                  src="/images/arrow-bottom.svg"
                  height={1000}
                  width={1000}
                  alt="arrow"
                />
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  isWalletOpen ? "max-h-60" : "max-h-0"
                }`}
              >
                <div className="pl-8">
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass("/dashboard/deposit")}
                    href="/dashboard/deposit"
                  >
                    <span>واریز ها</span>
                  </Link>
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass("/dashboard/withdraw")}
                    href="/dashboard/withdraw"
                  >
                    <span>برداشت ها</span>
                  </Link>
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass("/dashboard/card-managment")}
                    href="/dashboard/card-managment"
                  >
                    <span>کارت‌های بانکی</span>
                  </Link>
                </div>
              </div>
            </div>
            <div>
              <button
                onClick={() => toggleMenu("history")}
                className={`flex items-center gap-x-4 h-14 px-3 w-full text-left cursor-pointer ${
                  isInSection(["/dashboard/history"])
                    ? "border-r-8 border-[#E6E7E8] rounded-r-lg"
                    : ""
                }`}
              >
                <Image
                  className="w-5 h-5"
                  src="/images/history.svg"
                  height={1000}
                  width={1000}
                  alt="avatar"
                />
                <span>سوابق</span>
                <Image
                  className={`w-4 h-4 ml-auto transform transition-transform duration-300 ${
                    isHistoryOpen ? "rotate-180" : ""
                  }`}
                  src="/images/arrow-bottom.svg"
                  height={1000}
                  width={1000}
                  alt="arrow"
                />
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  isHistoryOpen ? "max-h-60" : "max-h-0"
                }`}
              >
                <div className="pl-8">
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass(
                      "/dashboard/history/deposit"
                    )}
                    href="/dashboard/history/deposit"
                  >
                    <span>واریز</span>
                  </Link>
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass(
                      "/dashboard/history/withdraw"
                    )}
                    href="/dashboard/history/withdraw"
                  >
                    <span>برداشت</span>
                  </Link>
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass("/dashboard/history/order")}
                    href="/dashboard/history/order"
                  >
                    <span>سفارش</span>
                  </Link>
                </div>
              </div>
            </div>
            <Link
              onClick={handleLinkClick}
              className={getLinkClass("/dashboard/authenticate")}
              href="/dashboard/authenticate"
            >
              <Image
                className="w-5 h-5"
                src="/images/auth.svg"
                height={1000}
                width={1000}
                alt="avatar"
              />
              <span>احراز هویت</span>
            </Link>
            <Link
              onClick={handleLinkClick}
              className={getLinkClass("/dashboard/referral")}
              href="/dashboard/referral"
            >
              <Image
                className="w-5 h-5"
                src="/images/star.png"
                height={1000}
                width={1000}
                alt="referral"
              />
              <span>کسب درآمد</span>
            </Link>
            <Link
              onClick={handleLinkClick}
              className={getLinkClass("/dashboard/tickets")}
              href="/dashboard/tickets"
            >
              <Image
                className="w-5 h-5"
                src="/images/mail.svg"
                height={1000}
                width={1000}
                alt="avatar"
              />
              <span>تیکت پشتیبانی</span>
            </Link>
            <div>
              <button
                onClick={() => toggleMenu("profile")}
                className={`flex items-center gap-x-4 h-14 px-3 w-full text-left cursor-pointer ${
                  isInSection(["/dashboard/profile", "/dashboard/settings"])
                    ? "border-r-8 border-[#E6E7E8] rounded-r-lg"
                    : ""
                }`}
              >
                <Image
                  className="w-5 h-5"
                  src="/images/setting.svg"
                  height={1000}
                  width={1000}
                  alt="avatar"
                />
                <span>پروفایل</span>
                <Image
                  className={`w-4 h-4 ml-auto transform transition-transform duration-300 ${
                    isProfileOpen ? "rotate-180" : ""
                  }`}
                  src="/images/arrow-bottom.svg"
                  height={1000}
                  width={1000}
                  alt="arrow"
                />
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  isProfileOpen ? "max-h-60" : "max-h-0"
                }`}
              >
                <div className="pl-8">
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass(
                      "/dashboard/profile/messages"
                    )}
                    href="/dashboard/profile/messages"
                  >
                    <span>پیام ها</span>
                  </Link>
                  <Link
                    onClick={handleLinkClick}
                    className={getSubmenuLinkClass("/dashboard/settings")}
                    href="/dashboard/settings"
                  >
                    <span>تنظیمات</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
