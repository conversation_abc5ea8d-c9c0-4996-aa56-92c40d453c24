"use client";
import React, { useEffect, useState, useRef } from "react";
import Image from "next/image";
import { getProfile, getDailyLimits, getTransactionTotals, getUserCurrency, getKeyStatistics, getTradingTransactions, getUserLevelCurrent, getUserLevelNext, getUserLevelDetails, requestUserLevelUpgrade, getUserLevelDailyLimits } from "@/requests/dashboardRequest";
import Link from "next/link";
import toast from "react-hot-toast";
import { sliceNumber, formatCryptoAmount, formatFiatAmount } from "@/lib/helper/sliceNumber";
import { motion } from "framer-motion";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import jalaali from "jalaali-js";
import { AnimatePresence } from "framer-motion";

interface Order {
  status: string;
  statusBg: string;
  amount: string;
  totalPrice: string;
  type: string;
  typeColor: string;
  currency: string;
  currencyName: string;
  coin_icon: string;
  price: string;
  time: string;
}

// API Transaction interface
interface Transaction {
  id: number;
  type: string;
  amount: string;
  price: number | null;
  status: string;
  created_at: string;
  currency_details: {
    id: number;
    name: string;
    coin_type: string;
    coin_price: string;
    coin_icon: string;
  };
  type_description: string;
  swap_details?: {
    from_currency: string;
    to_currency: string;
    from_amount: string;
    to_amount: number;
    usd_value: number;
    fee_percentage: string;
    fee_amount: number;
  };
  buy_details?: {
    toman_amount: string;
    usd_amount: number;
    usd_rate: string;
    crypto_amount: number;
  };
  sell_details?: {
    crypto_amount: string;
    usd_amount: string;
    usd_rate: string;
    toman_amount: number;
  };
}

// Add at the top, after imports
const translateTransactionType = (type: string) => {
  switch (type) {
    case 'buy':
      return 'خرید';
    case 'sell':
      return 'فروش';
    case 'swap_in':
      return 'تبدیل (ورودی)';
    case 'swap_out':
      return 'تبدیل (خروجی)';
    case 'deposit':
      return 'واریز';
    case 'withdraw':
      return 'برداشت';
    default:
      return type;
  }
};

const Page: React.FC = () => {
  const [info, setInfo] = useState<{ tomanBalance?: string }>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    getProfileHandler();
  }, []);

  async function getProfileHandler() {
    setLoading(true);
    try {
      const result = await getProfile();
      if (result.isError) {
        toast.error("خطایی رخ داد");
      } else {
        setInfo(result.data);
      }
    } catch (error) {
      toast.error("خطا در دریافت اطلاعات");
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="space-y-6 p-4 md:p-6 relative z-10">
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-[#2FA766] opacity-5 rounded-full -mr-48 -mt-48 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-80 h-80 bg-blue-500 opacity-5 rounded-full -ml-40 -mb-40 blur-3xl"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:24px_24px] opacity-20 pointer-events-none"></div>

      {/* Quick Actions Toolbar */}
      <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-3 md:p-4 rounded-xl shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 relative overflow-hidden group">
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

        {/* Animated background elements */}
        <div className="absolute -top-10 -right-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl group-hover:bg-blue-500/20 transition-all duration-700"></div>
        <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-green-500/10 rounded-full blur-xl group-hover:bg-green-500/20 transition-all duration-700"></div>

        <div className="flex flex-wrap items-center justify-between gap-3 relative z-10">
          <div className="flex items-center">
            <div className="bg-gradient-to-r from-blue-500/20 to-green-500/20 p-2 rounded-lg mr-2 hidden md:flex">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h2 className="text-base md:text-lg font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">دسترسی سریع</h2>
          </div>

          <div className="flex flex-wrap items-center gap-2 md:gap-3">
            <button className="bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-green-600/80 hover:to-green-700/80 px-3 py-1.5 rounded-lg text-xs md:text-sm flex items-center shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 border border-[#353945]/50 hover:border-green-500/50 group/btn">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1.5 text-[#2FA766] group-hover/btn:text-white transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              <span className="group-hover/btn:text-white transition-colors">خرید سریع</span>
            </button>

            <button className="bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-red-600/80 hover:to-red-700/80 px-3 py-1.5 rounded-lg text-xs md:text-sm flex items-center shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 border border-[#353945]/50 hover:border-red-500/50 group/btn">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1.5 text-red-500 group-hover/btn:text-white transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
              <span className="group-hover/btn:text-white transition-colors">فروش سریع</span>
            </button>

            <Link href="/dashboard/swap" className="bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-blue-600/80 hover:to-blue-700/80 px-3 py-1.5 rounded-lg text-xs md:text-sm flex items-center shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 border border-[#353945]/50 hover:border-blue-500/50 group/btn">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1.5 text-blue-500 group-hover/btn:text-white transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
              <span className="group-hover/btn:text-white transition-colors">تبدیل ارز</span>
            </Link>

            <button className="bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-yellow-600/80 hover:to-yellow-700/80 px-3 py-1.5 rounded-lg text-xs md:text-sm flex items-center shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 border border-[#353945]/50 hover:border-yellow-500/50 group/btn">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1.5 text-yellow-500 group-hover/btn:text-white transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span className="group-hover/btn:text-white transition-colors">معاملات آنی</span>
            </button>

            <button className="bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-purple-600/80 hover:to-purple-700/80 px-3 py-1.5 rounded-lg text-xs md:text-sm flex items-center shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 border border-[#353945]/50 hover:border-purple-500/50 group/btn">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1.5 text-purple-500 group-hover/btn:text-white transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <span className="group-hover/btn:text-white transition-colors">نمودارها</span>
            </button>
          </div>
        </div>
      </div>

      {/* Key Stats Section */}
      <KeyStatsCard />

      {/* First row - Enhanced with animations and effects */}
      <div className="flex flex-col md:flex-row gap-6 relative z-10">
        {/* Toman Wallet Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-4 rounded-xl w-full md:w-1/2 flex items-center justify-center shadow-[0_5px_15px_-5px_rgba(0,0,0,0.3)] border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 group relative overflow-hidden"
          whileHover={{
            boxShadow: "0 10px 20px -5px rgba(0,0,0,0.4)",
            y: -3
          }}
        >
          {/* Decorative elements */}
          <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-20 pointer-events-none"></div>
          <div
            className="absolute top-0 left-1/2 -translate-x-1/2 w-[50%] h-[2px] shadow-[0px_0px_10px_2px_rgba(249,115,22,0.5)]"
            style={{ background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(249,115,22,1) 50%, rgba(211,211,211,0.1) 100%)' }}
          />

          <div className="flex flex-col items-center w-full relative z-10">
            <div className="flex items-center gap-x-5 justify-right w-full">
              <motion.div
                className="bg-gradient-to-br from-orange-500/30 to-orange-600/20 p-3 rounded-xl shadow-[inset_0_0_10px_rgba(249,115,22,0.3)] border border-orange-500/20"
                whileHover={{ scale: 1.05 }}
                animate={{
                  boxShadow: ["inset 0 0 10px rgba(249,115,22,0.2)", "inset 0 0 15px rgba(249,115,22,0.4)", "inset 0 0 10px rgba(249,115,22,0.2)"],
                }}
                transition={{
                  boxShadow: { repeat: Infinity, duration: 2 },
                }}
              >
                <motion.div
                  animate={{
                    rotate: [0, 5, 0, -5, 0],
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <Image
                    src="/images/wallet2.png"
                    width={48}
                    height={48}
                    alt="wallet"
                    className="w-10 h-10 md:w-12 md:h-12 group-hover:scale-110 transition-transform duration-300"
                  />
                </motion.div>
              </motion.div>

              <div className="space-y-2">
                <motion.p
                  className="text-lg md:text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  کیف پول تومانی
                </motion.p>

                {loading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-5 h-5 border-2 border-t-orange-500 border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
                    <p className="text-gray-400">
                      در حال بارگذاری...
                    </p>
                  </div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    <div className="bg-gradient-to-r from-[#23262F] to-[#1C1E24] p-2 rounded-lg border border-gray-800/30 shadow-inner">
                      <p className="text-base md:text-lg font-bold bg-gradient-to-r from-orange-400 to-orange-300 bg-clip-text text-transparent">
                        {sliceNumber(Number(info.tomanBalance).toFixed(0))} <span className="text-xs font-normal text-gray-400">تومان</span>
                      </p>
                    </div>
                  </motion.div>
                )}
              </div>
            </div>

            <motion.div
              className="flex gap-x-3 mt-5 justify-center w-full"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <Link
                href="/dashboard/wallet-charge"
                className="group relative bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-400 hover:to-orange-500 text-white py-2 px-3 rounded-lg w-1/2 text-center font-bold shadow-md hover:shadow-orange-500/20 transition-all duration-300 hover:-translate-y-1 flex items-center justify-center overflow-hidden text-sm"
              >
                {/* Animated glow effect */}
                <motion.div
                  className="absolute top-0 left-0 right-0 h-full w-20 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                  animate={{ x: ['-100%', '200%'] }}
                  transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 1 }}
                />

                <span className="flex items-center justify-center bg-orange-500/30 rounded-full p-1 mr-1.5 group-hover:bg-orange-500/40 transition-all">
                  <Image
                    className="w-3.5 h-3.5"
                    src="/images/arrow-down.png"
                    height={14}
                    width={14}
                    alt="واریز"
                  />
                </span>
                واریز
              </Link>

              <Link
                href="/dashboard/withdraw"
                className="group relative bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white py-2 px-3 rounded-lg w-1/2 text-center font-bold shadow-md hover:shadow-blue-500/20 transition-all duration-300 hover:-translate-y-1 flex items-center justify-center overflow-hidden text-sm"
              >
                {/* Animated glow effect */}
                <motion.div
                  className="absolute top-0 left-0 right-0 h-full w-20 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                  animate={{ x: ['-100%', '200%'] }}
                  transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 1, delay: 0.5 }}
                />

                <span className="flex items-center justify-center bg-blue-500/30 rounded-full p-1 mr-1.5 group-hover:bg-blue-500/40 transition-all">
                  <Image
                    className="w-3.5 h-3.5"
                    src="/images/arrow-bottom.png"
                    height={14}
                    width={14}
                    alt="برداشت"
                  />
                </span>
                برداشت
              </Link>
            </motion.div>
          </div>
        </motion.div>

        <UserLevelCard />
      </div>



      {/* Market Overview Section */}
      <div className="relative">


        <MarketOverview />

        {/* Market Insights */}
        <MarketInsights />
      </div>
      {/* Second row */}
      <div className="flex flex-col md:flex-row gap-6 relative z-10">
        {/* <TotalAssetsCard /> */}

      </div>
      {/* Special Features Section */}
      {/* <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-4 md:p-5 rounded-xl shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 relative overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>
        <div className="relative z-10">
          <h2 className="text-lg md:text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent mb-4 text-right">ویژگی‌های ویژه</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gradient-to-br from-[#23262F] to-[#2A2D38] p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/50 transition-all duration-300 hover:shadow-lg group">
              <div className="flex items-center mb-3">
                <div className="bg-blue-500/20 p-2 rounded-lg mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="text-base font-medium">احراز هویت دو مرحله‌ای</h3>
              </div>
              <p className="text-sm text-gray-400 text-right">امنیت حساب خود را با فعال‌سازی احراز هویت دو مرحله‌ای افزایش دهید.</p>
              <button className="mt-3 text-blue-400 text-sm hover:text-blue-300 transition-colors flex items-center">
                فعال‌سازی
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>

            <div className="bg-gradient-to-br from-[#23262F] to-[#2A2D38] p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/50 transition-all duration-300 hover:shadow-lg group">
              <div className="flex items-center mb-3">
                <div className="bg-purple-500/20 p-2 rounded-lg mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-base font-medium">کیف پول سخت‌افزاری</h3>
              </div>
              <p className="text-sm text-gray-400 text-right">کیف پول سخت‌افزاری خود را برای امنیت بیشتر به حساب متصل کنید.</p>
              <button className="mt-3 text-purple-400 text-sm hover:text-purple-300 transition-colors flex items-center">
                اتصال
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>

            <div className="bg-gradient-to-br from-[#23262F] to-[#2A2D38] p-4 rounded-xl border border-[#353945]/30 hover:border-[#353945]/50 transition-all duration-300 hover:shadow-lg group">
              <div className="flex items-center mb-3">
                <div className="bg-green-500/20 p-2 rounded-lg mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="text-base font-medium">معاملات خودکار</h3>
              </div>
              <p className="text-sm text-gray-400 text-right">با تنظیم استراتژی‌های معاملاتی، به صورت خودکار معامله کنید.</p>
              <button className="mt-3 text-green-400 text-sm hover:text-green-300 transition-colors flex items-center">
                تنظیم
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div> */}

      {/* News & Announcements Section */}
      {/* <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-4 md:p-5 rounded-xl shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 relative overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-4">
            <button className="bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-[#2A2D38] hover:to-[#353945] px-3 py-1.5 rounded-lg text-xs flex items-center shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 border border-[#353945]/50 hover:border-[#353945]/80">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1.5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              بروزرسانی
            </button>
            <h2 className="text-lg md:text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">اخبار و اطلاعیه‌ها</h2>
          </div>

          <div className="space-y-3">
            <div className="bg-[#23262F]/50 p-3 rounded-lg border border-[#353945]/30 hover:border-[#353945]/50 transition-all duration-300 hover:bg-[#23262F]/70">
              <div className="flex justify-between items-start">
                <span className="bg-blue-500/20 text-blue-400 text-xs px-2 py-0.5 rounded">اطلاعیه</span>
                <h3 className="text-sm md:text-base font-medium text-right">راه‌اندازی ارز دیجیتال جدید در پلتفرم</h3>
              </div>
              <p className="text-xs md:text-sm text-gray-400 mt-2 text-right">به زودی ارز دیجیتال Solana به لیست ارزهای قابل معامله در پلتفرم اضافه خواهد شد.</p>
              <div className="flex justify-between items-center mt-2">
                <button className="text-xs text-blue-400 hover:text-blue-300 transition-colors">مشاهده بیشتر</button>
                <span className="text-xs text-gray-500">۲ ساعت پیش</span>
              </div>
            </div>

            <div className="bg-[#23262F]/50 p-3 rounded-lg border border-[#353945]/30 hover:border-[#353945]/50 transition-all duration-300 hover:bg-[#23262F]/70">
              <div className="flex justify-between items-start">
                <span className="bg-green-500/20 text-green-400 text-xs px-2 py-0.5 rounded">بروزرسانی</span>
                <h3 className="text-sm md:text-base font-medium text-right">بروزرسانی سیستم احراز هویت</h3>
              </div>
              <p className="text-xs md:text-sm text-gray-400 mt-2 text-right">سیستم احراز هویت پلتفرم بروزرسانی شده و امنیت بیشتری برای کاربران فراهم می‌کند.</p>
              <div className="flex justify-between items-center mt-2">
                <button className="text-xs text-blue-400 hover:text-blue-300 transition-colors">مشاهده بیشتر</button>
                <span className="text-xs text-gray-500">دیروز</span>
              </div>
            </div>

            <div className="bg-[#23262F]/50 p-3 rounded-lg border border-[#353945]/30 hover:border-[#353945]/50 transition-all duration-300 hover:bg-[#23262F]/70">
              <div className="flex justify-between items-start">
                <span className="bg-yellow-500/20 text-yellow-400 text-xs px-2 py-0.5 rounded">مهم</span>
                <h3 className="text-sm md:text-base font-medium text-right">تغییر در کارمزد معاملات</h3>
              </div>
              <p className="text-xs md:text-sm text-gray-400 mt-2 text-right">از تاریخ ۱۵ مهر، کارمزد معاملات برای کاربران سطح طلایی کاهش خواهد یافت.</p>
              <div className="flex justify-between items-center mt-2">
                <button className="text-xs text-blue-400 hover:text-blue-300 transition-colors">مشاهده بیشتر</button>
                <span className="text-xs text-gray-500">۲ روز پیش</span>
              </div>
            </div>
          </div>
        </div>
      </div> */}

      {/* Transaction History Section */}
      <TransactionHistory />
    </div>
  );
};

const UserLevelCard: React.FC = () => {
  const [currentLevel, setCurrentLevel] = useState<any>(null);
  const [nextLevel, setNextLevel] = useState<any>(null);
  const [levelDetails, setLevelDetails] = useState<any>(null);
  const [dailyLimits, setDailyLimits] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [upgradeLoading, setUpgradeLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'limits' | 'upgrade' | 'summary'>('limits');
  const [pendingNationalIdMessage, setPendingNationalIdMessage] = useState<string | null>(null);

  useEffect(() => {
    fetchLevelData();
  }, []);

  const fetchLevelData = async () => {
    try {
      setLoading(true);
      setPendingNationalIdMessage(null);
      // Fetch current level
      const currentResult = await getUserLevelCurrent();
      console.log('currentResult.data:', currentResult.data);
      // Special case: check for pending national id message
      if (
        !currentResult.isError &&
        currentResult.data &&
        typeof currentResult.data.success === 'boolean' &&
        currentResult.data.success === false &&
        currentResult.data.message &&
        currentResult.data.message.includes('کد ملی شما در حال استعلام')
      ) {
        setPendingNationalIdMessage(currentResult.data.message);
        setCurrentLevel(null);
        setNextLevel(null);
        setLevelDetails(null);
        setDailyLimits(null);
        return;
      }
      if (!currentResult.isError && currentResult.data) {
        setCurrentLevel(currentResult.data.current_level);
      }
      // Fetch next level
      const nextResult = await getUserLevelNext();
      if (!nextResult.isError && nextResult.data) {
        setNextLevel(nextResult.data.next_level);
      }
      // Fetch level details
      const detailsResult = await getUserLevelDetails();
      if (!detailsResult.isError && detailsResult.data) {
        setLevelDetails(detailsResult.data);
      }
      // Fetch daily limits
      const dailyLimitsResult = await getUserLevelDailyLimits();
      if (!dailyLimitsResult.isError && dailyLimitsResult.data) {
        setDailyLimits(dailyLimitsResult.data);
      }
    } catch (error) {
      console.error('Error fetching level data:', error);
      toast.error("خطا در دریافت اطلاعات سطح کاربر");
    } finally {
      setLoading(false);
    }
  };

  const handleUpgradeRequest = async () => {
    try {
      setUpgradeLoading(true);
      const result = await requestUserLevelUpgrade();
      
      if (result.isError) {
        toast.error(result.message || "خطا در درخواست ارتقا");
      } else {
        if (result.data.success) {
          toast.success("درخواست ارتقا با موفقیت ثبت شد");
          fetchLevelData(); // Refresh data
        } else {
          toast.error(result.data.message || "شرایط ارتقا برآورده نشده است");
        }
      }
    } catch (error) {
      console.error('Error requesting upgrade:', error);
      toast.error("خطا در درخواست ارتقا");
    } finally {
      setUpgradeLoading(false);
    }
  };

  // Get level color based on level name
  const getLevelColor = (levelName: string) => {
    switch (levelName) {
      case "طلایی":
        return "from-[#FFD700] to-[#FFA500]";
      case "نقره‌ای":
        return "from-[#C0C0C0] to-[#A8A8A8]";
      case "الماس":
        return "from-[#B9F2FF] to-[#87CEEB]";
      default:
        return "from-[#A2845E] to-[#8A6D4D]"; // برنزی
    }
  };

  // Get level icon based on level name
  const getLevelIcon = (levelName: string) => {
    switch (levelName) {
      case "طلایی":
        return "🥇";
      case "نقره‌ای":
        return "🥈";
      case "الماس":
        return "💎";
      default:
        return "🥉"; // برنزی
    }
  };

  // Calculate upgrade progress
  const calculateUpgradeProgress = () => {
    if (!levelDetails?.conditions?.details) return 0;
    
    const details = levelDetails.conditions.details;
    const totalPurchases = details.total_purchases;
    const timeRequirement = details.time_requirement;
    
    if (totalPurchases && timeRequirement) {
      const purchaseProgress = totalPurchases.met ? 50 : (parseFloat(totalPurchases.current) / parseFloat(totalPurchases.required)) * 50;
      const timeProgress = timeRequirement.met ? 50 : (timeRequirement.current_hours / timeRequirement.required_hours) * 50;
      return Math.min(100, purchaseProgress + timeProgress);
    }
    
    return 0;
  };

  const upgradeProgress = calculateUpgradeProgress();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
      className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] rounded-xl w-full md:w-1/2 shadow-[0_8px_32px_-8px_rgba(0,0,0,0.4)] border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 group relative overflow-hidden"
      whileHover={{
        boxShadow: "0 12px 40px -12px rgba(0,0,0,0.5)",
        y: -5
      }}
    >
      {/* Animated background */}
      <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-10"></div>
      <motion.div
        className="absolute top-0 left-1/2 -translate-x-1/2 w-[60%] h-[3px]"
        style={{ 
          background: 'linear-gradient(90deg, rgba(211,211,211,0.1) 0%, rgba(162,132,94,1) 50%, rgba(211,211,211,0.1) 100%)',
          boxShadow: '0px 0px 15px 3px rgba(162,132,94,0.6)'
        }}
      />

      {/* Only show the message if pendingNationalIdMessage is set */}
      {pendingNationalIdMessage ? (
        <div className="flex flex-col items-center justify-center py-12 w-full">
          <div className="bg-yellow-100/10 border border-yellow-400/40 rounded-xl px-6 py-8 text-center max-w-md mx-auto">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16 8 8 0 000 16z" />
            </svg>
            <p className="text-yellow-300 text-lg font-bold mb-2">در حال استعلام کد ملی</p>
            <p className="text-yellow-200 text-sm">{pendingNationalIdMessage}</p>
          </div>
        </div>
      ) : (
        <>
          {/* Header */}
          <div className="p-6 pb-4 relative z-10">
            <div className="flex justify-between items-center mb-6">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <h3 className="text-xl md:text-2xl mb-1 font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  سطح کاربری
                </h3>
                <p className="text-xs text-[#B1B5C3]">
                  مدیریت محدودیت‌ها و ارتقا سطح
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className={`bg-gradient-to-r ${getLevelColor(currentLevel?.name || "برنزی")} p-2 rounded-xl h-[40px] w-[120px] md:w-[140px] flex items-center justify-center shadow-lg relative overflow-hidden`}
                whileHover={{ scale: 1.05 }}
              >
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                  animate={{ x: ['-100%', '200%'] }}
                  transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
                />
                <div className="flex items-center text-center">
                  <span className="text-sm font-bold text-white">
                    {getLevelIcon(currentLevel?.name || "برنزی")} {currentLevel?.name || "برنزی"}
                  </span>
                </div>
              </motion.div>
            </div>

            {/* Tab Navigation */}
            <div className="flex space-x-1 mb-4 bg-[#23262F]/50 rounded-lg p-1">
              {[
                { id: 'limits', label: 'محدودیت‌ها', icon: '📊' },
                { id: 'upgrade', label: 'ارتقا', icon: '🚀' },
                { id: 'summary', label: 'خلاصه', icon: '📋' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-xs font-medium transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-[#2FA766] to-[#25855A] text-white shadow-md'
                      : 'text-gray-400 hover:text-gray-300 hover:bg-[#2A2D38]/50'
                  }`}
                >
                  <span className="ml-1">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="px-6 pb-6 relative z-10">
            {loading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex justify-between items-center">
                    <div className="h-4 w-20 bg-gray-700/50 rounded animate-pulse"></div>
                    <div className="h-6 w-24 bg-gray-700/50 rounded animate-pulse"></div>
                  </div>
                ))}
              </div>
            ) : currentLevel ? (
              <>
                {/* Limits Tab */}
                {activeTab === 'limits' && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="space-y-4"
                  >
                    {/* Buy Limits */}
                    <div className="bg-[#23262F]/30 rounded-lg p-4 border border-[#353945]/30">
                      <div className="flex justify-between items-center mb-3">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-green-500 rounded-full ml-2"></div>
                          <span className="text-sm font-medium">خرید روزانه</span>
                        </div>
                        <span className="text-sm text-green-400 font-bold">
                          {dailyLimits?.buy_limits?.formatted_remaining_today || currentLevel?.daily_buy_limit}
                        </span>
                      </div>
                      {dailyLimits?.buy_limits && (
                        <>
                          <div className="text-xs text-gray-400 mb-2">
                            {dailyLimits.buy_limits.formatted_used_today} / {dailyLimits.buy_limits.formatted_daily_limit}
                          </div>
                          <div className="w-full bg-[#1A1D1F] rounded-full h-2">
                            <motion.div
                              className="bg-gradient-to-r from-green-500 to-green-400 h-2 rounded-full"
                              initial={{ width: 0 }}
                              animate={{ 
                                width: `${Math.min(100, (dailyLimits.buy_limits.used_today / dailyLimits.buy_limits.daily_limit) * 100)}%` 
                              }}
                              transition={{ duration: 1, delay: 0.3 }}
                            />
                          </div>
                        </>
                      )}
                    </div>

                    {/* Sell Limits */}
                    <div className="bg-[#23262F]/30 rounded-lg p-4 border border-[#353945]/30">
                      <div className="flex justify-between items-center mb-3">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-blue-500 rounded-full ml-2"></div>
                          <span className="text-sm font-medium">فروش روزانه</span>
                        </div>
                        <span className="text-sm text-blue-400 font-bold">
                          {dailyLimits?.sell_limits?.formatted_remaining_today || currentLevel?.daily_sell_limit}
                        </span>
                      </div>
                      {dailyLimits?.sell_limits && (
                        <>
                          <div className="text-xs text-gray-400 mb-2">
                            {dailyLimits.sell_limits.formatted_used_today} / {dailyLimits.sell_limits.formatted_daily_limit}
                          </div>
                          <div className="w-full bg-[#1A1D1F] rounded-full h-2">
                            <motion.div
                              className="bg-gradient-to-r from-blue-500 to-blue-400 h-2 rounded-full"
                              initial={{ width: 0 }}
                              animate={{ 
                                width: `${Math.min(100, (dailyLimits.sell_limits.used_today / dailyLimits.sell_limits.daily_limit) * 100)}%` 
                              }}
                              transition={{ duration: 1, delay: 0.4 }}
                            />
                          </div>
                        </>
                      )}
                    </div>

                    {/* Withdrawal Limits */}
                    <div className="bg-[#23262F]/30 rounded-lg p-4 border border-[#353945]/30">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-purple-500 rounded-full ml-2"></div>
                          <span className="text-sm font-medium">برداشت روزانه</span>
                        </div>
                        <span className="text-sm text-purple-400 font-bold">
                          {dailyLimits?.withdrawal_limits?.formatted_daily_limit || currentLevel?.daily_withdrawal_limit}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Upgrade Tab */}
                {activeTab === 'upgrade' && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="space-y-4"
                  >
                    {/* Next Level Preview */}
                    {nextLevel && (
                      <div className="bg-gradient-to-br from-[#23262F]/70 to-[#2A2D38]/70 rounded-lg p-4 border border-[#353945]/30">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm text-gray-400">سطح بعدی</span>
                          <span className="text-sm font-bold text-yellow-400 flex items-center">
                            {getLevelIcon(nextLevel.name)} {nextLevel.name}
                            <motion.div
                              animate={{ rotate: [0, 10, -10, 0] }}
                              transition={{ duration: 2, repeat: Infinity }}
                              className="ml-1"
                            >
                              ⭐
                            </motion.div>
                          </span>
                        </div>
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          <div className="text-center">
                            <div className="text-green-400 font-medium">{nextLevel.daily_buy_limit}</div>
                            <div className="text-gray-500">خرید</div>
                          </div>
                          <div className="text-center">
                            <div className="text-blue-400 font-medium">{nextLevel.daily_sell_limit}</div>
                            <div className="text-gray-500">فروش</div>
                          </div>
                          <div className="text-center">
                            <div className="text-purple-400 font-medium">{nextLevel.daily_withdrawal_limit}</div>
                            <div className="text-gray-500">برداشت</div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Upgrade Progress */}
                    {levelDetails && (
                      <div className="bg-[#23262F]/30 rounded-lg p-4 border border-[#353945]/30">
                        <div className="flex justify-between items-center mb-3">
                          <span className="text-sm font-medium">پیشرفت ارتقا</span>
                          <span className="text-sm font-bold bg-gradient-to-r from-[#2FA766] to-[#25855A] text-transparent bg-clip-text">
                            {upgradeProgress.toFixed(1)}%
                          </span>
                        </div>
                        <div className="w-full bg-[#1A1D1F] rounded-full h-3 mb-3">
                          <motion.div
                            className="bg-gradient-to-r from-[#2FA766] to-[#25855A] h-3 rounded-full relative"
                            initial={{ width: 0 }}
                            animate={{ width: `${upgradeProgress}%` }}
                            transition={{ duration: 1, delay: 0.5 }}
                          >
                            <motion.div
                              className="absolute top-0 left-0 right-0 h-full w-20 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                              animate={{ x: ['-100%', '400%'] }}
                              transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
                            />
                          </motion.div>
                        </div>

                        {/* Upgrade Conditions */}
                        {levelDetails.conditions?.reasons && (
                          <div className="space-y-2">
                            {levelDetails.conditions.reasons.map((reason: string, index: number) => (
                              <motion.div
                                key={index}
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}
                                className="text-xs text-gray-400 flex items-center"
                              >
                                <motion.span
                                  animate={{ scale: [1, 1.2, 1] }}
                                  transition={{ duration: 1, repeat: Infinity, delay: index * 0.5 }}
                                  className="w-1 h-1 bg-gray-500 rounded-full ml-2"
                                />
                                {reason}
                              </motion.div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Upgrade Button */}
                    {levelDetails && (
                      <button
                        onClick={handleUpgradeRequest}
                        disabled={upgradeLoading || !levelDetails.can_upgrade}
                        className={`w-full py-3 px-4 rounded-lg text-sm font-bold transition-all duration-300 ${
                          levelDetails.can_upgrade
                            ? "bg-gradient-to-r from-[#2FA766] to-[#25855A] hover:from-[#25855A] hover:to-[#1A5F3F] text-white shadow-lg hover:shadow-xl hover:-translate-y-1"
                            : "bg-[#23262F] text-gray-500 cursor-not-allowed"
                        } flex items-center justify-center relative overflow-hidden`}
                      >
                        {levelDetails.can_upgrade && (
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
                            animate={{ x: ['-100%', '200%'] }}
                            transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
                          />
                        )}
                        
                        {upgradeLoading ? (
                          <div className="w-4 h-4 border-2 border-t-white border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin ml-2"></div>
                        ) : (
                          <motion.svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 ml-2"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                            animate={levelDetails.can_upgrade ? { rotate: [0, 10, -10, 0] } : {}}
                            transition={{ duration: 2, repeat: Infinity }}
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                          </motion.svg>
                        )}
                        <span className="relative z-10">
                          {levelDetails.can_upgrade ? "درخواست ارتقا سطح" : "شرایط ارتقا برآورده نشده"}
                        </span>
                      </button>
                    )}
                  </motion.div>
                )}

                {/* Summary Tab */}
                {activeTab === 'summary' && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="space-y-4"
                  >
                    {/* Today's Summary */}
                    {dailyLimits?.today_transactions && (
                      <div className="bg-gradient-to-br from-[#1A1D1F]/70 to-[#23262F]/70 rounded-lg p-4 border border-[#353945]/30">
                        <div className="flex items-center justify-between mb-4">
                          <span className="text-sm font-medium">خلاصه امروز</span>
                          <span className="text-xs text-gray-500">{dailyLimits.date}</span>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-center">
                            <div className="text-lg font-bold text-green-400">
                              {dailyLimits.today_transactions.formatted_total_buy}
                            </div>
                            <div className="text-xs text-gray-500">کل خرید</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-bold text-blue-400">
                              {dailyLimits.today_transactions.formatted_total_sell}
                            </div>
                            <div className="text-xs text-gray-500">کل فروش</div>
                          </div>
                        </div>
                        
                        <div className="mt-4 pt-4 border-t border-[#353945]/30">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">جریان خالص</span>
                            <span className={`text-lg font-bold ${
                              dailyLimits.today_transactions.net_flow > 0 ? 'text-green-400' : 'text-red-400'
                            }`}>
                              {dailyLimits.today_transactions.formatted_net_flow}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Reset Info */}
                    <div className="bg-[#23262F]/30 rounded-lg p-4 border border-[#353945]/30">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-400">بازنشانی محدودیت‌ها</span>
                        <span className="text-sm font-medium text-blue-400">{dailyLimits?.next_reset}</span>
                      </div>
                    </div>
                  </motion.div>
                )}
              </>
            ) : (
              <div className="text-center py-8 text-gray-400">
                خطا در دریافت اطلاعات سطح کاربر
              </div>
            )}
          </div>
        </>
      )}
    </motion.div>
  );
};

const TotalAssetsCard: React.FC = () => {
  const [transactionData, setTransactionData] = useState<{
    total_withdrawals: number;
    total_deposits: number;
    total_purchases: number;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [totalAssets, setTotalAssets] = useState(0);

  useEffect(() => {
    fetchTransactionTotals();
  }, []);

  const fetchTransactionTotals = async () => {
    try {
      setLoading(true);
      const result = await getTransactionTotals();
      if (result.isError) {
        toast.error(result?.message ? `خطا: ${result.message}` : "خطا در دریافت اطلاعات تراکنش‌ها");
      } else {
        setTransactionData(result.data);
        // Calculate total assets (sum of deposits minus withdrawals plus purchases)
        const total = (result.data.total_deposits || 0) - (result.data.total_withdrawals || 0) + (result.data.total_purchases || 0);
        setTotalAssets(total);
      }
    } catch (error) {
      const errMsg = (error as any)?.message;
      toast.error(errMsg ? `خطا: ${errMsg}` : "خطا در دریافت اطلاعات تراکنش‌ها");
    } finally {
      setLoading(false);
    }
  };

  // Chart configuration
  const chartOptions = {
    chart: {
      backgroundColor: "transparent",
      height: 200,
    },
    title: {
      text: "",
    },
    credits: {
      enabled: false,
    },
    xAxis: {
      categories: ["فروردین", "اردیبهشت", "خرداد", "تیر", "مرداد", "شهریور"],
      labels: {
        style: {
          color: "#777E90",
          fontSize: "10px",
        },
      },
      lineColor: "transparent",
      tickColor: "transparent",
    },
    yAxis: {
      title: {
        text: "",
      },
      gridLineColor: "rgba(53, 57, 69, 0.2)",
      labels: {
        enabled: false,
      },
    },
    legend: {
      enabled: false,
    },
    tooltip: {
      backgroundColor: "#23262F",
      borderColor: "#353945",
      borderRadius: 8,
      style: {
        color: "#fff",
      },
      formatter: function(this: any): string {
        return `<b>${this.y.toLocaleString()} تومان</b>`;
      },
    },
    plotOptions: {
      areaspline: {
        fillColor: {
          linearGradient: {
            x1: 0,
            y1: 0,
            x2: 0,
            y2: 1,
          },
          stops: [
            [0, "rgba(47, 167, 102, 0.3)"],
            [1, "rgba(47, 167, 102, 0.05)"],
          ],
        },
        lineColor: "#2FA766",
        lineWidth: 2,
        marker: {
          enabled: false,
          fillColor: "#2FA766",
          lineColor: "#23262F",
          lineWidth: 2,
          radius: 4,
          states: {
            hover: {
              enabled: true,
            },
          },
        },
        states: {
          hover: {
            lineWidth: 2,
          },
        },
      },
    },
    series: [
      {
        type: "areaspline",
        name: "دارایی",
        data: [
          totalAssets * 0.85,
          totalAssets * 0.9,
          totalAssets * 0.88,
          totalAssets * 0.95,
          totalAssets * 0.92,
          totalAssets,
        ],
      },
    ],
  };

  return (
    <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-4 md:p-5 rounded-2xl w-full md:w-1/2 lg:max-w-[450px] shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 group relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-[#2FA766] opacity-5 rounded-full -mr-16 -mt-16 group-hover:opacity-10 transition-opacity"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-blue-500 opacity-5 rounded-full -ml-12 -mb-12 group-hover:opacity-10 transition-opacity"></div>

      <div className="relative z-10">
        <h2 className="text-right text-lg md:text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">مجموع دارایی ها</h2>
        <p className="text-right flex items-center justify-end mt-3 md:mt-5 text-base md:text-lg font-bold">
          <span className="bg-gradient-to-r from-[#103923] to-[#0D2D1C] rounded-full text-xs md:text-sm px-3 py-1.5 flex items-center ml-4 md:ml-6 shadow-md">
            <Image
              className="w-2.5 h-2.5 md:w-3.5 md:h-3.5 ml-1.5 md:ml-2"
              src="/images/up.png"
              height={14}
              width={14}
              alt="افزایش"
            />
            <span className="text-[#2FA766]">6.3%</span>
          </span>
          {loading ? (
            <span className="bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent animate-pulse">در حال بارگذاری...</span>
          ) : (
            <span className="bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">{sliceNumber(totalAssets.toFixed(0))} تومان</span>
          )}
        </p>

        <div className="flex justify-end space-x-reverse space-x-3 md:space-x-4 mt-4 md:mt-6">
          <button className="group bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-[#2A2D38] hover:to-[#353945] rounded-xl p-2 md:p-3 text-xs md:text-sm flex items-center shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 border border-[#353945]/50 hover:border-[#353945]/80">
            <span className="p-0.5 md:p-1">لیست دارایی ها</span>
            <span className="flex items-center justify-center bg-gray-700/30 rounded-full p-1 ml-1.5 group-hover:bg-gray-700/50 transition-all">
              <Image
                className="w-3 h-3 md:w-4 md:h-4"
                src="/images/wall.png"
                height={16}
                width={16}
                alt="لیست"
              />
            </span>
          </button>
          <button className="group bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-[#2A2D38] hover:to-[#353945] rounded-xl p-2 md:p-2.5 text-xs md:text-sm flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 border border-[#353945]/50 hover:border-[#353945]/80">
            <span className="p-0.5 md:p-1">برداشت</span>
            <span className="flex items-center justify-center bg-gray-700/30 rounded-full p-1 ml-1.5 group-hover:bg-gray-700/50 transition-all">
              <Image
                className="w-3 h-3 md:w-4 md:h-4"
                src="/images/top.png"
                height={16}
                width={16}
                alt="برداشت"
              />
            </span>
          </button>
          <button className="group bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-[#2A2D38] hover:to-[#353945] rounded-xl p-2 md:p-2.5 text-xs md:text-sm flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 border border-[#353945]/50 hover:border-[#353945]/80">
            <span className="p-0.5 md:p-1">واریز</span>
            <span className="flex items-center justify-center bg-gray-700/30 rounded-full p-1 ml-1.5 group-hover:bg-gray-700/50 transition-all">
              <Image
                className="w-3 h-3 md:w-4 md:h-4"
                src="/images/down.png"
                height={16}
                width={16}
                alt="واریز"
              />
            </span>
          </button>
        </div>

        <div className="mt-4 md:mt-6 bg-gradient-to-br from-[#1A1D1F] to-[#23262F] rounded-xl p-3 h-[160px] md:h-[200px] relative shadow-inner border border-[#353945]/30 group-hover:border-[#353945]/50 transition-all">
          <div className="absolute w-full h-full flex flex-col justify-between">
            <div className="border-t border-gray-800/50 w-full"></div>
            <div className="border-t border-gray-800/50 w-full"></div>
            <div className="border-t border-gray-800/50 w-full"></div>
            <div className="border-t border-gray-800/50 w-full"></div>
          </div>
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-pulse text-gray-400">در حال بارگذاری نمودار...</div>
            </div>
          ) : (
            <HighchartsReact highcharts={Highcharts} options={chartOptions} />
          )}
        </div>

        {transactionData ? (
          <AssetGrid
            totalPurchases={transactionData.total_purchases}
            totalDeposits={transactionData.total_deposits}
            totalWithdrawals={transactionData.total_withdrawals}
          />
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 mt-6 md:mt-8">
            <div className="flex items-center justify-end bg-[#23262F]/50 p-3 rounded-xl animate-pulse h-16"></div>
            <div className="flex items-center justify-end bg-[#23262F]/50 p-3 rounded-xl animate-pulse h-16"></div>
            <div className="flex items-center justify-end bg-[#23262F]/50 p-3 rounded-xl animate-pulse h-16"></div>
            <div className="flex items-center justify-end bg-[#23262F]/50 p-3 rounded-xl animate-pulse h-16"></div>
          </div>
        )}
      </div>
    </div>
  );
};

interface AssetGridProps {
  totalPurchases: number;
  totalDeposits: number;
  totalWithdrawals: number;
}

const AssetGrid: React.FC<AssetGridProps> = ({ totalPurchases, totalDeposits, totalWithdrawals }) => (
  <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 mt-6 md:mt-8">
    <AssetItem
      title={sliceNumber(totalPurchases.toFixed(0)) + " تومان"}
      description="مجموع خرید ها تا کنون"
      imageSrc="/images/basketdown.png"
      alt="مجموع خرید ها"
      bgColor="from-green-500/10 to-green-600/5"
      borderColor="border-green-500/20"
      hoverBorderColor="group-hover:border-green-500/30"
    />
    <AssetItem
      title={sliceNumber(totalDeposits.toFixed(0)) + " تومان"}
      description="مجموع واریزی ها تا کنون"
      imageSrc="/images/arrow-down.png"
      alt="مجموع واریزی ها"
      bgColor="from-blue-500/10 to-blue-600/5"
      borderColor="border-blue-500/20"
      hoverBorderColor="group-hover:border-blue-500/30"
    />
    <AssetItem
      title={sliceNumber(totalPurchases.toFixed(0)) + " تومان"}
      description="مجموع فروش ها تا کنون"
      imageSrc="/images/basketup.png"
      alt="مجموع فروش ها"
      bgColor="from-red-500/10 to-red-600/5"
      borderColor="border-red-500/20"
      hoverBorderColor="group-hover:border-red-500/30"
    />
    <AssetItem
      title={sliceNumber(totalWithdrawals.toFixed(0)) + " تومان"}
      description="مجموع برداشت ها تا کنون"
      imageSrc="/images/arrow-bottom.png"
      alt="مجموع برداشت ها"
      bgColor="from-orange-500/10 to-orange-600/5"
      borderColor="border-orange-500/20"
      hoverBorderColor="group-hover:border-orange-500/30"
    />
  </div>
);

interface AssetItemProps {
  title: string;
  description: string;
  imageSrc: string;
  alt: string;
  bgColor: string;
  borderColor: string;
  hoverBorderColor: string;
}

const AssetItem: React.FC<AssetItemProps> = ({
  title,
  description,
  imageSrc,
  alt,
  bgColor,
  borderColor,
  hoverBorderColor,
}) => (
  <div className="flex items-center justify-end bg-[#23262F]/50 p-3 rounded-xl hover:bg-[#23262F]/70 transition-all duration-300 border border-[#353945]/30 hover:border-[#353945]/50">
    <div className="flex flex-col items-end min-w-0">
      <p className="text-right whitespace-nowrap text-sm md:text-base font-medium bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
        {title}
      </p>
      <span className="text-right text-[10px] md:text-sm text-gray-400 whitespace-nowrap">
        {description}
      </span>
    </div>
    <div className="flex-shrink-0 ml-3 md:ml-4 mr-1 md:mr-2 flex items-center h-full">
      <div className={`bg-gradient-to-br ${bgColor} p-2 rounded-lg shadow-inner ${borderColor} ${hoverBorderColor} transition-all duration-300 hover:scale-110`}>
        <Image
          className="w-5 h-5 md:w-6 md:h-6"
          src={imageSrc}
          height={24}
          width={24}
          alt={alt}
        />
      </div>
    </div>
  </div>
);

interface KeyStatisticsData {
  profit_loss: number;
  profit_loss_toman: number;
  today_transactions: any[];
  today_transactions_count: number;
  total_asset_value: number;
  total_asset_value_toman: number;
}

const KeyStatsCard: React.FC = () => {
  const [keyStats, setKeyStats] = useState<KeyStatisticsData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchKeyStatistics();
  }, []);

  const fetchKeyStatistics = async () => {
    try {
      setLoading(true);
      const result = await getKeyStatistics();
      if (result.isError) {
        toast.error(result.message || "خطا در دریافت آمار کلیدی");
      } else {
        setKeyStats(result.data);
      }
    } catch (error) {
      toast.error("خطا در دریافت آمار کلیدی");
    } finally {
      setLoading(false);
    }
  };

  // Calculate profit/loss percentage
  const profitLossPercentage = keyStats && keyStats.total_asset_value > 0
    ? ((keyStats.profit_loss / keyStats.total_asset_value) * 100).toFixed(1)
    : "0.0";

  const isProfitPositive = keyStats && keyStats.profit_loss >= 0;

  return (
    <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-4 rounded-xl shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 relative overflow-hidden group">
      <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

      <div className="flex items-center justify-between mb-4 relative z-10">
        <div className="flex items-center gap-2">
          <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 p-1.5 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="text-sm md:text-base font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">آمار کلیدی</h3>
        </div>
        <div className="flex items-center">
          <button onClick={fetchKeyStatistics} className="text-xs text-gray-400 hover:text-white transition-colors flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 ml-1 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            بروزرسانی
          </button>
        </div>
      </div>

      {loading ? (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4 relative z-10">
          {[1, 2, 3, 4].map((item) => (
            <div key={item} className="bg-[#23262F]/70 p-3 rounded-lg border border-[#353945]/30 h-24 animate-pulse">
              <div className="flex items-center justify-between mb-2">
                <div className="bg-gray-700 h-6 w-6 rounded-full"></div>
                <div className="bg-gray-700 h-3 w-10 rounded"></div>
              </div>
              <div className="space-y-2">
                <div className="bg-gray-700 h-3 w-20 rounded"></div>
                <div className="bg-gray-700 h-5 w-16 rounded"></div>
                <div className="bg-gray-700 h-3 w-12 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4 relative z-10">
          {/* Total Asset Value */}
          <div className="bg-[#23262F]/70 p-3 rounded-lg border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 group-hover:shadow-md">
            <div className="flex items-center justify-between mb-2">
              <div className="bg-blue-500/20 p-1.5 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <span className="text-[10px] text-gray-400">24h</span>
            </div>
            <div className="text-right">
              <p className="text-xs text-gray-400">ارزش کل دارایی</p>
              <p className="text-sm md:text-base font-bold">${keyStats ? keyStats.total_asset_value : "0.00"}</p>
              <p className="text-[10px] text-gray-400 mt-1">{keyStats ? sliceNumber(keyStats.total_asset_value_toman.toFixed(0)) : "0"} تومان</p>
            </div>
          </div>

          {/* Profit/Loss */}
          <div className="bg-[#23262F]/70 p-3 rounded-lg border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 group-hover:shadow-md">
            <div className="flex items-center justify-between mb-2">
              <div className={`${isProfitPositive ? "bg-green-500/20" : "bg-red-500/20"} p-1.5 rounded-full`}>
                <svg xmlns="http://www.w3.org/2000/svg" className={`h-3.5 w-3.5 ${isProfitPositive ? "text-green-400" : "text-red-400"}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={isProfitPositive ? "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" : "M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"} />
                </svg>
              </div>
              <span className="text-[10px] text-gray-400">کل</span>
            </div>
            <div className="text-right">
              <p className="text-xs text-gray-400">سود/زیان</p>
              <p className={`text-sm md:text-base font-bold ${isProfitPositive ? "text-green-400" : "text-red-400"}`}>
                {isProfitPositive ? "+" : "-"}${keyStats ? sliceNumber(Math.abs(keyStats.profit_loss).toFixed(2)) : "0.00"}
              </p>
              <div className="flex items-center justify-end mt-1">
                <span className={`text-[10px] ${isProfitPositive ? "text-green-400" : "text-red-400"}`}>
                  {isProfitPositive ? "+" : "-"}{profitLossPercentage}%
                </span>
                <svg xmlns="http://www.w3.org/2000/svg" className={`h-3 w-3 ${isProfitPositive ? "text-green-400" : "text-red-400"} mr-0.5`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={isProfitPositive ? "M5 15l7-7 7 7" : "M19 9l-7 7-7-7"} />
                </svg>
              </div>
            </div>
          </div>

          {/* Today's Transactions */}
          <div className="bg-[#23262F]/70 p-3 rounded-lg border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 group-hover:shadow-md">
            <div className="flex items-center justify-between mb-2">
              <div className="bg-purple-500/20 p-1.5 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 11V9a2 2 0 00-2-2m2 4v4a2 2 0 104 0v-1m-4-3H9m2 0h4m6 1a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <span className="text-[10px] text-gray-400">امروز</span>
            </div>
            <div className="text-right">
              <p className="text-xs text-gray-400">معاملات امروز</p>
              <p className="text-sm md:text-base font-bold">{keyStats ? keyStats.today_transactions_count : 0}</p>
              <div className="flex items-center justify-end mt-1">
                <span className="text-[10px] text-blue-400">تراکنش</span>
              </div>
            </div>
          </div>

          {/* Latest Transaction */}
          <div className="bg-[#23262F]/70 p-3 rounded-lg border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 group-hover:shadow-md">
            <div className="flex items-center justify-between mb-2">
              <div className="bg-yellow-500/20 p-1.5 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <span className="text-[10px] text-gray-400">آخرین</span>
            </div>
            <div className="text-right">
              <p className="text-xs text-gray-400">آخرین تراکنش</p>
              {keyStats && keyStats.today_transactions.length > 0 ? (
                <>
                  <p className="text-sm md:text-base font-bold">
                    {/* Show type_description if it is not empty and not equal to the type code, otherwise show Farsi translation */}
                    {keyStats.today_transactions[0].type_description && keyStats.today_transactions[0].type_description !== keyStats.today_transactions[0].type
                      ? keyStats.today_transactions[0].type_description
                      : translateTransactionType(keyStats.today_transactions[0].type || "")}
                  </p>
                  <div className="flex items-center justify-end mt-1">
                    <span className="text-[10px] text-gray-400 truncate max-w-[100px]">
                      {translateTransactionType(keyStats.today_transactions[0].type || "")}
                    </span>
                  </div>
                </>
              ) : (
                <>
                  <p className="text-sm md:text-base font-bold">-</p>
                  <div className="flex items-center justify-end mt-1">
                    <span className="text-[10px] text-gray-400">بدون تراکنش</span>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
interface CurrencyItem {
  name: string;
  coin_icon: string;
  coin_type: string;
  balance_usd: string;
  balance: string;
  balance_toman: number;
  id: number;
}

const MarketOverview: React.FC = () => {
  const [userCurrency, setUserCurrency] = useState<CurrencyItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    getCurrencyHandler();
  }, []);

  const getCurrencyHandler = async () => {
    setLoading(true);
    try {
      const result = await getUserCurrency();
      // Ensure result.data is an array and filter out id === 6
      if (Array.isArray(result.data)) {
        setUserCurrency(result.data.filter((item) => item.id !== 6));
      } else {
        setUserCurrency([]);
      }
    } catch (error) {
      toast.error("خطا در دریافت اطلاعات ارزها");
    } finally {
      setLoading(false);
    }
  };

  // Helper function to parse comma-separated numbers
  const parseNumber = (value: string): number => {
    if (!value) return 0;
    // Remove commas and convert to number
    return parseFloat(value.replace(/,/g, '')) || 0;
  };

  const toggleViewMode = () => {
    setViewMode(viewMode === 'grid' ? 'list' : 'grid');
  };

  return (
    <div className="mt-12 relative">
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-[#2FA766] opacity-5 rounded-full -mr-32 -mt-32 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-48 h-48 bg-[#2FA766] opacity-5 rounded-full -ml-24 -mb-24 blur-3xl"></div>

      <div className="flex items-center justify-between mb-6 relative z-10">
        <h3 className="text-right text-xl md:text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
          نمای بازار دارایی شما:
        </h3>
        <div className="flex items-center gap-2">
          <motion.button
            onClick={toggleViewMode}
            className="bg-[#23262F] hover:bg-[#2A2D38] p-2 rounded-lg transition-all duration-300 flex items-center shadow-md hover:shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 relative overflow-hidden group"
            title={viewMode === 'grid' ? "نمایش لیستی" : "نمایش شبکه‌ای"}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            initial={{ backgroundColor: "#23262F" }}
            animate={{
              backgroundColor: viewMode === 'grid' ? "#23262F" : "#2A2D38",
              borderColor: viewMode === 'grid' ? "rgba(53, 57, 69, 0.5)" : "rgba(59, 130, 246, 0.3)"
            }}
            transition={{ duration: 0.3 }}
          >
            {/* Animated background effect */}
            <motion.div
              className="absolute inset-0 bg-blue-500/10 opacity-0 group-hover:opacity-100"
              initial={{ opacity: 0 }}
              animate={{ opacity: viewMode === 'grid' ? 0 : 0.2 }}
              transition={{ duration: 0.3 }}
            />

            <motion.div
              initial={{ rotate: 0 }}
              animate={{ rotate: viewMode === 'grid' ? 0 : 180 }}
              transition={{ duration: 0.5, type: "spring", stiffness: 200 }}
              className="relative z-10"
            >
              {viewMode === 'grid' ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              )}
            </motion.div>
          </motion.button>
          <button onClick={getCurrencyHandler} className="bg-[#23262F] hover:bg-[#2A2D38] p-2 rounded-lg transition-colors duration-300 flex items-center shadow-md hover:shadow-lg border border-[#353945]/50 hover:border-[#353945]/80">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#2FA766]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>
      {loading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((item) => (
            <div
              key={item}
              className="bg-gradient-to-br from-[#23262F] to-[#1C1E24] p-4 rounded-xl w-full h-[200px] md:h-[220px] flex flex-col items-center justify-center border border-[#353945] shadow-lg relative overflow-hidden"
            >
              <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-30"></div>
              <div className="w-14 h-14 border-4 border-t-[#2FA766] border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mb-4 relative z-10"></div>
              <p className="text-gray-400 text-sm relative z-10">در حال بارگذاری...</p>
            </div>
          ))}
        </div>
      ) : viewMode === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 relative z-10">
          {Array.isArray(userCurrency) && userCurrency.map((item) => {
            // Parse the balance values properly
            const balanceUsd = parseNumber(item.balance_usd);
            const balance = parseNumber(item.balance);
            
            return (
              <MarketCard
                key={item.coin_type}
                id={item.id}
                imageSrc={`https://api.exchangim.com/storage/${item.coin_icon}`}
                alt={item.coin_type}
                title={item.coin_type}
                balance_usd={balanceUsd.toFixed(2)}
                balance_toman={sliceNumber(Number(item.balance_toman).toFixed(0))}
                subValue={balance.toFixed(2) + " " + item.coin_type}
                change="+2.5%"
                changeColor="bg-[#103923] text-[#2FA766]"
              />
            );
          })}
        </div>
      ) : (
        <div className="flex flex-col gap-4 relative z-10">
          {Array.isArray(userCurrency) && userCurrency.map((item) => {
            // Parse the balance values properly
            const balanceUsd = parseNumber(item.balance_usd);
            const balance = parseNumber(item.balance);
            
            return (
              <MarketListItem
                key={item.coin_type}
                id={item.id}
                imageSrc={`https://api.exchangim.com/storage/${item.coin_icon}`}
                alt={item.coin_type}
                title={item.coin_type}
                balance_usd={balanceUsd.toFixed(2)}
                balance_toman={sliceNumber(Number(item.balance_toman).toFixed(0))}
                subValue={balance.toFixed(2) + " " + item.coin_type}
                change="+2.5%"
                changeColor="bg-[#103923] text-[#2FA766]"
              />
            );
          })}
        </div>
      )}
    </div>
  );
};

interface MarketCardProps {
  imageSrc: string;
  alt: string;
  title: string;
  balance_usd: string;
  balance_toman: string;
  subValue: string;
  change: string;
  changeColor: string;
  id: number;
}

interface CryptoMarketData {
  id: string;
  name: string;
  symbol: string;
  current_price: number;
  price_change_percentage_24h: number;
  image: string;
}

const MarketInsights: React.FC = () => {
  const [cryptoData, setCryptoData] = useState<CryptoMarketData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCryptoData = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(
        "https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&ids=bitcoin,ethereum,solana&order=market_cap_desc&per_page=3&page=1&sparkline=false&price_change_percentage=24h"
      );

      if (!response.ok) {
        throw new Error("Failed to fetch data from CoinGecko API");
      }

      const data = await response.json();
      setCryptoData(data);
    } catch (err) {
      console.error("Error fetching crypto data:", err);
      setError(err instanceof Error ? err.message : "An unknown error occurred");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCryptoData();
  }, []);

  // Generate a simple chart path based on price change
  const generateChartPath = (priceChange: number) => {
    const isPositive = priceChange >= 0;

    if (isPositive) {
      return "M0,20 Q30,15 60,18 T120,12 T180,16 T240,8";
    } else {
      return "M0,10 Q30,12 60,10 T120,15 T180,12 T240,18";
    }
  };

  return (
    <div className="mt-6 bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-4 rounded-xl shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 relative overflow-hidden group">
      <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

      <div className="flex items-center justify-between mb-4 relative z-10">
        <button
          onClick={fetchCryptoData}
          className="bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-[#2A2D38] hover:to-[#353945] px-3 py-1.5 rounded-lg text-xs flex items-center shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 border border-[#353945]/50 hover:border-[#353945]/80"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1.5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          بروزرسانی
        </button>
        <h3 className="text-base md:text-lg font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">تحلیل بازار</h3>
      </div>

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 relative z-10">
          {[1, 2, 3].map((index) => (
            <div key={index} className="bg-[#23262F]/70 p-3 rounded-lg border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 group-hover:shadow-md">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <div className="bg-blue-500/20 p-1.5 rounded-full animate-pulse w-7 h-7"></div>
                  <div className="mr-2 h-4 bg-gray-700 rounded animate-pulse w-16"></div>
                </div>
                <div className="h-4 bg-gray-700 rounded animate-pulse w-12"></div>
              </div>
              <div className="h-12 relative bg-[#2A2D38]/50 rounded animate-pulse"></div>
              <div className="flex justify-between items-center mt-2">
                <div className="h-4 bg-gray-700 rounded animate-pulse w-16"></div>
                <div className="h-4 bg-gray-700 rounded animate-pulse w-20"></div>
              </div>
            </div>
          ))}
        </div>
      ) : error ? (
        <div className="bg-[#23262F]/70 p-4 rounded-lg border border-red-500/30 text-center">
          <p className="text-red-400 mb-2">خطا در دریافت اطلاعات</p>
          <p className="text-xs text-gray-400">{error}</p>
          <button
            onClick={fetchCryptoData}
            className="mt-3 bg-[#2A2D38] hover:bg-[#353945] px-3 py-1.5 rounded-lg text-xs transition-colors"
          >
            تلاش مجدد
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 relative z-10">
          {cryptoData.map((crypto) => {
            const priceChange = crypto.price_change_percentage_24h;
            const isPositive = priceChange >= 0;
            const changeColor = isPositive ? "text-green-400" : "text-red-400";
            const iconBgColor = isPositive ? "bg-blue-500/20" : "bg-red-500/20";
            const iconColor = isPositive ? "text-blue-400" : "text-red-400";
            const chartStroke = isPositive ? "#22c55e" : "#ef4444";
            const chartPath = generateChartPath(priceChange);

            return (
              <div key={crypto.id} className="bg-[#23262F]/70 p-3 rounded-lg border border-[#353945]/30 hover:border-[#353945]/70 transition-all duration-300 group-hover:shadow-md">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <div className={`${iconBgColor} p-1.5 rounded-full`}>
                      {isPositive ? (
                        <svg xmlns="http://www.w3.org/2000/svg" className={`h-3.5 w-3.5 ${iconColor}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                      ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" className={`h-3.5 w-3.5 ${iconColor}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                        </svg>
                      )}
                    </div>
                    <span className="mr-2 text-xs font-medium">{crypto.name}</span>
                  </div>
                  <div className="flex items-center">
                    <span className={`text-[10px] ${changeColor}`}>
                      {isPositive ? "+" : ""}{priceChange.toFixed(2)}%
                    </span>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-3 w-3 ${changeColor} mr-0.5`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={isPositive ? "M5 15l7-7 7 7" : "M19 9l-7 7-7-7"} />
                    </svg>
                  </div>
                </div>
                <div className="h-12 relative">
                  <svg className="w-full h-full" preserveAspectRatio="none">
                    <path
                      d={chartPath}
                      fill="none"
                      stroke={chartStroke}
                      strokeWidth="1.5"
                      strokeOpacity="0.5"
                    />
                  </svg>
                </div>
                <div className="flex justify-between items-center mt-2">
                  <span className="text-xs text-gray-400">قیمت فعلی</span>
                  <span className="text-sm font-medium">${crypto.current_price.toLocaleString()}</span>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

// List item component uses the same props as the card
const MarketListItem: React.FC<MarketCardProps> = ({
  imageSrc,
  alt,
  title,
  balance_usd,
  balance_toman,
  id,
  change,
  changeColor,
  subValue,
}) => {
  return (
    <div className="group bg-gradient-to-br from-[#23262F] to-[#1C1E24] p-4 rounded-xl w-full border border-[#353945] shadow-lg hover:shadow-[0_0_15px_rgba(47,167,102,0.15)] transition-all duration-300 hover:-translate-y-1 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-24 h-24 bg-[#2FA766] opacity-5 rounded-full -mr-10 -mt-10 group-hover:opacity-10 transition-opacity"></div>
      <div className="absolute bottom-0 left-0 w-16 h-16 bg-[#2FA766] opacity-5 rounded-full -ml-8 -mb-8 group-hover:opacity-10 transition-opacity"></div>

      {/* Background pattern */}
      <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-30"></div>

      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 relative z-10">
        <div className="flex items-center">
          <div className="relative w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-[#353945] to-[#23262F] rounded-full p-1 mr-3 shadow-inner flex items-center justify-center">
            <Image
              className="w-7 h-7 md:w-8 md:h-8"
              src={imageSrc}
              height={32}
              width={32}
              alt={alt}
              unoptimized
              onError={(e) => {
                console.error('Image failed to load:', imageSrc);
                e.currentTarget.src = '/images/coin.png';
              }}
            />
          </div>
          <div>
            <div className="flex items-center gap-2">
              <span className="text-lg md:text-xl font-medium bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">{title}</span>
              <div className={`${changeColor} px-2 py-1 rounded-full text-xs md:text-sm flex items-center`}>
                <Image
                  className="w-2 h-2 md:w-3 md:h-3 ml-1"
                  src="/images/up.png"
                  height={12}
                  width={12}
                  alt="تغییرات"
                />
                {change}
              </div>
            </div>
            <div className="flex items-center text-xs text-gray-400 mt-1">
              <span>{subValue}</span>
              <span className="mx-1.5">|</span>
              <span>{balance_toman} تومان</span>
            </div>
          </div>

          {/* Mini Chart */}
          <div className="w-24 h-8 relative hidden md:block">
            <svg className="w-full h-full" preserveAspectRatio="none">
              <path
                d="M0,20 Q10,15 20,18 T40,12 T60,16 T80,8"
                fill="none"
                stroke="#22c55e"
                strokeWidth="1.5"
                strokeOpacity="0.5"
              />
            </svg>
          </div>
        </div>

        <div className="flex items-center">
          <div className="flex-shrink-0 mr-4">
            <p className="text-lg md:text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">  {balance_usd} $ </p>
          </div>

          <div className="flex gap-2 flex-wrap">
            <Link
              href={`/dashboard/buy/?id=${id}`}
              className="group bg-gradient-to-br from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white py-1.5 px-3 rounded-lg text-xs md:text-sm text-center font-bold transition-all duration-300 shadow-md hover:shadow-xl flex items-center justify-center border border-green-500/30 hover:border-green-400/50 hover:-translate-y-0.5"
            >
              <span className="flex items-center justify-center bg-green-500/20 rounded-full p-0.5 md:p-1 mr-1 md:mr-1.5 group-hover:bg-green-500/30 transition-all">
                <Image
                  className="w-3 h-3 md:w-4 md:h-4"
                  src="/images/down.png"
                  height={16}
                  width={16}
                  alt="خرید"
                />
              </span>
              خرید
            </Link>
            <Link
              href={`/dashboard/sell/?id=${id}`}
              className="group bg-gradient-to-br from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white py-1.5 px-3 rounded-lg text-xs md:text-sm text-center font-bold transition-all duration-300 shadow-md hover:shadow-xl flex items-center justify-center border border-red-500/30 hover:border-red-400/50 hover:-translate-y-0.5"
            >
              <span className="flex items-center justify-center bg-red-500/20 rounded-full p-0.5 md:p-1 mr-1 md:mr-1.5 group-hover:bg-red-500/30 transition-all">
                <Image
                  className="w-3 h-3 md:w-4 md:h-4"
                  src="/images/top.png"
                  height={16}
                  width={16}
                  alt="فروش"
                />
              </span>
              فروش
            </Link>
            <Link
              href={`/dashboard/deposit/?id=${id}`}
              className="group bg-gradient-to-br from-orange-500 to-orange-600 hover:from-orange-400 hover:to-orange-500 text-white py-1.5 px-3 rounded-lg text-xs md:text-sm text-center font-bold transition-all duration-300 shadow-md hover:shadow-xl flex items-center justify-center border border-orange-500/30 hover:border-orange-400/50 hover:-translate-y-0.5"
            >
              <span className="flex items-center justify-center bg-orange-500/20 rounded-full p-0.5 md:p-1 mr-1 md:mr-1.5 group-hover:bg-orange-500/30 transition-all">
                <Image
                  className="w-3 h-3 md:w-4 md:h-4"
                  src="/images/arrow-down.png"
                  height={16}
                  width={16}
                  alt="واریز"
                />
              </span>
              واریز
            </Link>
            <Link
              href={`/dashboard/withdraw?id=${id}`}
              className="group bg-gradient-to-br from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white py-1.5 px-3 rounded-lg text-xs md:text-sm text-center font-bold transition-all duration-300 shadow-md hover:shadow-xl flex items-center justify-center border border-blue-500/30 hover:border-blue-400/50 hover:-translate-y-0.5"
            >
              <span className="flex items-center justify-center bg-blue-500/20 rounded-full p-0.5 md:p-1 mr-1 md:mr-1.5 group-hover:bg-blue-500/30 transition-all">
                <Image
                  className="w-3 h-3 md:w-4 md:h-4"
                  src="/images/arrow-bottom.png"
                  height={16}
                  width={16}
                  alt="برداشت"
                />
              </span>
              برداشت
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

const MarketCard: React.FC<MarketCardProps> = ({
  imageSrc,
  alt,
  title,
  balance_usd,
  balance_toman,
  id,
  // Removed unused variables: change, changeColor
  subValue,
}) => {
  return (
    <div className="group flex flex-col justify-between bg-gradient-to-br from-[#23262F] to-[#1C1E24] p-4 rounded-xl w-full h-auto min-h-[220px] md:min-h-[280px] border border-[#353945] shadow-lg hover:shadow-[0_0_15px_rgba(47,167,102,0.15)] transition-all duration-300 hover:-translate-y-1 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-24 h-24 bg-[#2FA766] opacity-5 rounded-full -mr-10 -mt-10 group-hover:opacity-10 transition-opacity"></div>
      <div className="absolute bottom-0 left-0 w-16 h-16 bg-[#2FA766] opacity-5 rounded-full -ml-8 -mb-8 group-hover:opacity-10 transition-opacity"></div>

      {/* Background pattern */}
      <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-30"></div>

      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center p-1.5 rounded-md cursor-pointer">
            <div className="relative w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-[#353945] to-[#23262F] rounded-full p-1 mr-2 shadow-inner flex items-center justify-center">
              <Image
                className="w-6 h-6 md:w-7 md:h-7"
                src={imageSrc}
                height={28}
                width={28}
                alt={alt}
                unoptimized
                onError={(e) => {
                  console.error('Image failed to load:', imageSrc);
                  e.currentTarget.src = '/images/coin.png';
                }}
              />
            </div>
            <span className="text-base md:text-lg font-medium bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">{title}</span>
          </div>
          {/* <div className={`${changeColor} px-2 py-1 rounded-full text-xs md:text-sm flex items-center`}>
            <Image
              className="w-2 h-2 md:w-3 md:h-3 ml-1"
              src="/images/up.png"
              height={12}
              width={12}
              alt="تغییرات"
            />
            {change}
          </div> */}
        </div>
        <div className="flex flex-col sm:items-center sm:flex-row justify-between mb-3">
          <p className="text-base md:text-lg font-bold text-right mb-2 sm:mb-0 text-gray-200">
            موجودی:
          </p>
          <div className="flex flex-col justify-center items-end w-full gap-y-1.5">
            <p className="text-lg sm:text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">$ {balance_usd}</p>
            <div className="flex items-center">
              <p className="text-xs sm:text-sm text-gray-400">
                {balance_toman} تومان
              </p>
              <span className="mx-1 text-gray-500">|</span>
              <p className="text-xs sm:text-sm text-gray-400">{subValue}</p>
            </div>
          </div>
        </div>

        {/* Mini Chart */}
        <div className="w-full h-8 mt-1 mb-2 md:mb-0 relative">
          <div className="absolute inset-0 flex items-end">
            <div className="h-1/3 w-1/12 bg-gradient-to-t from-green-500/20 to-green-500/5 rounded-sm"></div>
            <div className="h-2/3 w-1/12 bg-gradient-to-t from-green-500/20 to-green-500/5 rounded-sm"></div>
            <div className="h-1/2 w-1/12 bg-gradient-to-t from-green-500/20 to-green-500/5 rounded-sm"></div>
            <div className="h-1/3 w-1/12 bg-gradient-to-t from-green-500/20 to-green-500/5 rounded-sm"></div>
            <div className="h-2/3 w-1/12 bg-gradient-to-t from-green-500/20 to-green-500/5 rounded-sm"></div>
            <div className="h-full w-1/12 bg-gradient-to-t from-green-500/20 to-green-500/5 rounded-sm"></div>
            <div className="h-2/3 w-1/12 bg-gradient-to-t from-green-500/20 to-green-500/5 rounded-sm"></div>
            <div className="h-1/3 w-1/12 bg-gradient-to-t from-green-500/20 to-green-500/5 rounded-sm"></div>
            <div className="h-1/2 w-1/12 bg-gradient-to-t from-green-500/20 to-green-500/5 rounded-sm"></div>
            <div className="h-2/3 w-1/12 bg-gradient-to-t from-green-500/20 to-green-500/5 rounded-sm"></div>
            <div className="h-full w-1/12 bg-gradient-to-t from-green-500/20 to-green-500/5 rounded-sm"></div>
            <div className="h-2/3 w-1/12 bg-gradient-to-t from-green-500/20 to-green-500/5 rounded-sm"></div>
          </div>
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-dashed border-green-500/20"></div>
          </div>
          <div className="absolute inset-0">
            <svg className="w-full h-full" preserveAspectRatio="none">
              <path
                d="M0,20 Q30,15 60,18 T120,12 T180,16 T240,8 T300,14 T360,4 T420,10 T480,6"
                fill="none"
                stroke="#22c55e"
                strokeWidth="1.5"
                strokeOpacity="0.5"
              />
            </svg>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-3 mt-auto pt-2 md:pt-6 relative z-10">
        <Link
          href={`/dashboard/buy/?id=${id}`}
          className="group bg-gradient-to-br from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white py-1.5 px-1.5 md:py-2 md:px-2 rounded-lg md:rounded-xl text-xs md:text-sm text-center font-bold transition-all duration-300 shadow-md hover:shadow-xl flex items-center justify-center border border-green-500/30 hover:border-green-400/50 hover:-translate-y-0.5"
        >
          <span className="flex items-center justify-center bg-green-500/20 rounded-full p-0.5 md:p-1 mr-1 md:mr-1.5 group-hover:bg-green-500/30 transition-all">
            <Image
              className="w-3 h-3 md:w-4 md:h-4"
              src="/images/down.png"
              height={16}
              width={16}
              alt="خرید"
            />
          </span>
          خرید
        </Link>
        <Link
          href={`/dashboard/sell/?id=${id}`}
          className="group bg-gradient-to-br from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white py-1.5 px-1.5 md:py-2 md:px-2 rounded-lg md:rounded-xl text-xs md:text-sm text-center font-bold transition-all duration-300 shadow-md hover:shadow-xl flex items-center justify-center border border-red-500/30 hover:border-red-400/50 hover:-translate-y-0.5"
        >
          <span className="flex items-center justify-center bg-red-500/20 rounded-full p-0.5 md:p-1 mr-1 md:mr-1.5 group-hover:bg-red-500/30 transition-all">
            <Image
              className="w-3 h-3 md:w-4 md:h-4"
              src="/images/top.png"
              height={16}
              width={16}
              alt="فروش"
            />
          </span>
          فروش
        </Link>
        <Link
          href={`/dashboard/deposit/?id=${id}`}
          className="group bg-gradient-to-br from-orange-500 to-orange-600 hover:from-orange-400 hover:to-orange-500 text-white py-1.5 px-1.5 md:py-2 md:px-2 rounded-lg md:rounded-xl text-xs md:text-sm text-center font-bold transition-all duration-300 shadow-md hover:shadow-xl flex items-center justify-center border border-orange-500/30 hover:border-orange-400/50 hover:-translate-y-0.5"
        >
          <span className="flex items-center justify-center bg-orange-500/20 rounded-full p-0.5 md:p-1 mr-1 md:mr-1.5 group-hover:bg-orange-500/30 transition-all">
            <Image
              className="w-3 h-3 md:w-4 md:h-4"
              src="/images/arrow-down.png"
              height={16}
              width={16}
              alt="واریز"
            />
          </span>
          واریز
        </Link>
        <Link
          href={`/dashboard/withdraw?id=${id}`}
          className="group bg-gradient-to-br from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white py-1.5 px-1.5 md:py-2 md:px-2 rounded-lg md:rounded-xl text-xs md:text-sm text-center font-bold transition-all duration-300 shadow-md hover:shadow-xl flex items-center justify-center border border-blue-500/30 hover:border-blue-400/50 hover:-translate-y-0.5"
        >
          <span className="flex items-center justify-center bg-blue-500/20 rounded-full p-0.5 md:p-1 mr-1 md:mr-1.5 group-hover:bg-blue-500/30 transition-all">
            <Image
              className="w-3 h-3 md:w-4 md:h-4"
              src="/images/arrow-bottom.png"
              height={16}
              width={16}
              alt="برداشت"
            />
          </span>
          برداشت
        </Link>
      </div>
    </div>
  );
};

interface TransactionHistoryProps {}

const TransactionHistory: React.FC<TransactionHistoryProps> = () => {
  const [loading, setLoading] = useState(true);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [transactionType, setTransactionType] = useState('');
  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const transactionOptions = [
    { value: '', label: 'همه' },
    { value: 'buy', label: 'خرید' },
    { value: 'sell', label: 'فروش' },
    { value: 'swap_in', label: 'تبدیل (ورودی)' },
    { value: 'swap_out', label: 'تبدیل (خروجی)' },
    { value: 'deposit', label: 'واریز' },
    { value: 'withdraw', label: 'برداشت' },
  ];

  useEffect(() => {
    fetchTransactions();
  }, [currentPage, transactionType]);

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      const result = await getTradingTransactions(currentPage, transactionType);
      if (result.isError) {
        toast.error(result?.message ? `خطا: ${result.message}` : "خطا در دریافت اطلاعات تراکنش‌ها");
      } else {
        setTransactions(result.data.data);
        if (result.pagination) {
          setTotalPages(result.pagination.last_page);
        }
      }
    } catch (error) {
      const errMsg = (error as any)?.message;
      toast.error(errMsg ? `خطا: ${errMsg}` : "خطا در دریافت اطلاعات تراکنش‌ها");
    } finally {
      setLoading(false);
    }
  };

  // Reset to first page when changing transaction type
  const handleTypeChange = (type: string) => {
    setCurrentPage(1);
    setTransactionType(type);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const jDate = jalaali.toJalaali(date);
      return `${jDate.jy}/${jDate.jm}/${jDate.jd} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
    } catch (error) {
      return dateString;
    }
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'done':
        return { text: 'موفق', bgColor: 'bg-[#103923]' };
      case 'pending':
        return { text: 'در انتظار', bgColor: 'bg-[#3A3A3D]' };
      case 'rejected':
      case 'failed':
        return { text: 'ناموفق', bgColor: 'bg-[#5A1C1E]' };
      default:
        return { text: status, bgColor: 'bg-[#3A3A3D]' };
    }
  };

  const getTypeInfo = (type: string) => {
    switch (type) {
      case 'buy':
        return { text: 'خرید', color: 'text-[#2FA766]' };
      case 'sell':
        return { text: 'فروش', color: 'text-red-500' };
      case 'swap_in':
        return { text: 'تبدیل (ورودی)', color: 'text-blue-500' };
      case 'swap_out':
        return { text: 'تبدیل (خروجی)', color: 'text-orange-500' };
      case 'withdraw':
        return { text: 'برداشت', color: 'text-purple-500' };
      case 'deposit':
        return { text: 'واریز', color: 'text-green-500' };
      default:
        return { text: type, color: 'text-gray-500' };
    }
  };

  const transformTransactionToOrder = (transaction: Transaction): Order => {
    const statusInfo = getStatusInfo(transaction.status);
    const typeInfo = getTypeInfo(transaction.type);

    let price = transaction.price ? `${formatFiatAmount(transaction.price)} تومان` : '-';
    let totalPrice = '-';

    if (transaction.buy_details) {
      totalPrice = `${formatFiatAmount(transaction.buy_details.toman_amount)} تومان`;
    } else if (transaction.sell_details) {
      totalPrice = `${formatFiatAmount(transaction.sell_details.toman_amount)} تومان`;
    } else if (transaction.swap_details) {
      totalPrice = `${formatFiatAmount(transaction.swap_details.usd_value)} USD`;
    }

    // Format amount properly (crypto amount)
    const formattedAmount = formatCryptoAmount(transaction.amount);

    return {
      status: statusInfo.text,
      statusBg: statusInfo.bgColor,
      amount: formattedAmount,
      totalPrice,
      type: typeInfo.text,
      typeColor: typeInfo.color,
      currency: transaction.currency_details.coin_type,
      coin_icon: transaction.currency_details.coin_icon,
      currencyName: transaction.currency_details.name,
      price,
      time: formatDate(transaction.created_at),
    };
  };

  return (
    <div className="bg-gradient-to-br from-[#18191D] to-[#1C1E24] p-4 md:p-5 rounded-2xl shadow-lg border border-[#353945]/50 hover:border-[#353945]/80 transition-all duration-300 group relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-[#2FA766] opacity-5 rounded-full -mr-16 -mt-16 group-hover:opacity-10 transition-opacity"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-blue-500 opacity-5 rounded-full -ml-12 -mb-12 group-hover:opacity-10 transition-opacity"></div>
      <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:16px_16px] opacity-20 pointer-events-none"></div>

      <div className="flex flex-col md:flex-row justify-between items-center py-4 space-y-4 md:space-y-0 relative z-10">
        <h1 className="text-xl md:text-2xl font-bold text-center w-full md:text-start md:w-auto bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
          تاریخچه معاملات
        </h1>
        <div>
          <button
            onClick={() => setModalOpen(true)}
            className="px-4 py-2 rounded-lg bg-[#23262F] hover:bg-[#2A2D38] text-white text-xs md:text-sm flex items-center gap-2 border border-[#353945]/50"
            type="button"
          >
            <span>
              {transactionOptions.find(opt => opt.value === transactionType)?.label || 'نوع تراکنش'}
            </span>
            <svg className={`w-4 h-4 transition-transform`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
      </div>
      {modalOpen && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/60">
          <div className="bg-[#23262F] rounded-2xl shadow-2xl border border-[#353945]/50 w-[90vw] max-w-xs mx-auto p-6 flex flex-col items-center relative animate-fade-in">
            <button
              onClick={() => setModalOpen(false)}
              className="absolute top-3 left-3 text-gray-400 hover:text-white text-lg"
              aria-label="بستن"
            >
              ×
            </button>
            <h2 className="text-lg font-bold mb-6 text-white">انتخاب نوع تراکنش</h2>
            <div className="flex flex-col gap-2 w-full">
              {transactionOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => {
                    setCurrentPage(1);
                    setTransactionType(option.value);
                    setModalOpen(false);
                  }}
                  className={`w-full text-center px-4 py-2 rounded-lg text-sm transition-all duration-200 border border-[#353945]/30 hover:bg-[#2A2D38] focus:outline-none ${
                    transactionType === option.value ? 'bg-[#2FA766] text-white font-bold' : 'text-white'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {loading ? (
        <>
          {/* Loading skeleton for desktop */}
          <div className="hidden md:block overflow-x-auto relative z-10">
            <table className="w-full text-center">
              <thead className="text-xs text-[#B1B5C3] uppercase bg-gradient-to-r from-[#23262F] to-[#2A2D38] rounded-t-xl">
                <tr>
                  <th scope="col" className="px-4 md:px-6 py-4 first:rounded-tr-xl">
                    Date/Time
                  </th>
                  <th scope="col" className="px-4 md:px-6 py-4">
                    Pair
                  </th>
                  <th scope="col" className="px-4 md:px-6 py-4">
                    Type
                  </th>
                  <th scope="col" className="px-4 md:px-6 py-4">
                    Total Price
                  </th>
                  <th scope="col" className="px-4 md:px-6 py-4">
                    Amount
                  </th>
                  <th scope="col" className="px-4 md:px-6 py-4">
                    Status
                  </th>
                  <th scope="col" className="px-4 md:px-6 py-4 last:rounded-tl-xl">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="text-[#FCFCFD]">
                {[1, 2, 3, 4, 5].map((item) => (
                  <tr key={item} className="odd:bg-[#18191D]/80 even:bg-[#1C1E24]/80 border-b border-[#353945]/20">
                    <td className="py-4">
                      <div className="h-4 bg-gradient-to-r from-gray-700 to-gray-600 rounded-full animate-pulse mx-auto w-16"></div>
                    </td>

                    <td className="py-4">
                      <div className="flex items-center justify-center">
                        <div className="w-8 h-8 bg-gradient-to-r from-gray-700 to-gray-600 rounded-full animate-pulse mr-2"></div>
                        <div className="h-8 bg-gradient-to-r from-gray-700 to-gray-600 rounded-lg animate-pulse w-16"></div>
                      </div>
                    </td>
                    <td className="py-4">
                      <div className="h-5 bg-gradient-to-r from-gray-700 to-gray-600 rounded-full animate-pulse mx-auto w-12"></div>
                    </td>
                    <td className="py-4">
                      <div className="h-4 bg-gradient-to-r from-gray-700 to-gray-600 rounded-full animate-pulse mx-auto w-20"></div>
                    </td>
                    <td className="py-4">
                      <div className="h-4 bg-gradient-to-r from-gray-700 to-gray-600 rounded-full animate-pulse mx-auto w-8"></div>
                    </td>
                    <td className="py-4">
                      <div className="h-6 bg-gradient-to-r from-gray-700 to-gray-600 rounded-lg animate-pulse mx-auto w-16"></div>
                    </td>
                    <td className="py-4">
                      <div className="h-8 bg-gradient-to-r from-gray-700 to-gray-600 rounded-lg animate-pulse mx-auto w-20"></div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Loading skeleton for mobile */}
          <div className="md:hidden space-y-4 overflow-x-auto w-full relative z-10">
            {[1, 2, 3].map((item) => (
              <div
                key={item}
                className="bg-gradient-to-br from-[#1C1E24] to-[#23262F] p-4 rounded-xl flex flex-col gap-y-4 w-full shadow-md border border-[#353945]/30 relative overflow-hidden"
              >
                {/* Background pattern */}
                <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-20"></div>

                <div className="flex justify-between items-center relative z-10">
                  <span className="text-[#B1B5C3] text-xs font-medium">Date/Time:</span>
                  <div className="h-3 bg-gradient-to-r from-gray-700 to-gray-600 rounded-full animate-pulse w-16"></div>
                </div>

                <div className="flex justify-between items-center relative z-10">
                  <span className="text-[#B1B5C3] text-xs font-medium">Pair:</span>
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-gradient-to-r from-gray-700 to-gray-600 rounded-full animate-pulse mr-2"></div>
                    <div className="h-6 bg-gradient-to-r from-gray-700 to-gray-600 rounded-lg animate-pulse w-16"></div>
                  </div>
                </div>
                <div className="flex justify-between items-center relative z-10">
                  <span className="text-[#B1B5C3] text-xs font-medium">Type:</span>
                  <div className="h-4 bg-gradient-to-r from-gray-700 to-gray-600 rounded-full animate-pulse w-12"></div>
                </div>
                <div className="flex justify-between items-center relative z-10">
                  <span className="text-[#B1B5C3] text-xs font-medium">Total Price:</span>
                  <div className="h-3 bg-gradient-to-r from-gray-700 to-gray-600 rounded-full animate-pulse w-20"></div>
                </div>
                <div className="flex justify-between items-center relative z-10">
                  <span className="text-[#B1B5C3] text-xs font-medium">Amount:</span>
                  <div className="h-3 bg-gradient-to-r from-gray-700 to-gray-600 rounded-full animate-pulse w-8"></div>
                </div>
                <div className="flex justify-between items-center relative z-10">
                  <span className="text-[#B1B5C3] text-xs font-medium">Status:</span>
                  <div className="h-5 bg-gradient-to-r from-gray-700 to-gray-600 rounded-lg animate-pulse w-16"></div>
                </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        <>
          <div className="hidden md:block overflow-x-auto relative z-10">
            <table className="w-full text-center">
              <thead className="text-xs text-[#B1B5C3] uppercase bg-gradient-to-r from-[#23262F] to-[#2A2D38] rounded-t-xl">
                <tr>
                  <th scope="col" className="px-4 md:px-6 py-4 first:rounded-tr-xl">
                    Date/Time
                  </th>

                  <th scope="col" className="px-4 md:px-6 py-4">
                    Pair
                  </th>
                  <th scope="col" className="px-4 md:px-6 py-4">
                    Type
                  </th>
                  <th scope="col" className="px-4 md:px-6 py-4">
                    Total Price
                  </th>
                  <th scope="col" className="px-4 md:px-6 py-4">
                    Amount
                  </th>
                  <th scope="col" className="px-4 md:px-6 py-4">
                    Status
                  </th>
                  <th scope="col" className="px-4 md:px-6 py-4 last:rounded-tl-xl">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="text-[#FCFCFD]">
                {transactions.length > 0 ? (
                  transactions.map((transaction) => {
                    const item = transformTransactionToOrder(transaction);
                    return (
                      <tr
                        key={transaction.id}
                        className="odd:bg-[#18191D]/80 even:bg-[#1C1E24]/80 hover:bg-[#23262F]/80 transition-colors duration-200 border-b border-[#353945]/20"
                      >
                        <td className="py-4 text-xs md:text-sm">{item.time}</td>
                        <td className="py-4">
                          <div>
                            <p className="text-base md:text-lg font-medium bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                              {item.currency}
                            </p>
                            <p className="text-[10px] md:text-xs text-[#B1B5C3] font-light">
                              {item.currencyName}
                            </p>
                          </div>
                        </td>
                        <td className={`py-4 ${item.typeColor} text-xs md:text-sm font-medium`}>
                          {item.type}
                        </td>
                        <td className="py-4 text-xs md:text-sm font-medium">
                          {item.totalPrice}
                        </td>
                        <td className="py-4 text-xs md:text-sm">{item.amount}</td>
                        <td className="py-4">
                          <span
                            className={`${item.statusBg} text-[#C5EFD8] p-2 rounded-lg text-xs md:text-sm shadow-sm`}
                          >
                            {item.status}
                          </span>
                        </td>
                        <td className="py-4">
                          <Link
                            href={`/dashboard/transaction/${transaction.id}`}
                            className="bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-[#2A2D38] hover:to-[#353945] px-3 py-1.5 rounded-lg text-xs md:text-sm transition-colors border border-[#353945]/50 hover:border-[#353945]/80 inline-flex items-center justify-center"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            مشاهده
                          </Link>
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan={7} className="text-center py-10">
                      <div className="flex flex-col items-center justify-center">
                        <div className="w-16 h-16 bg-[#23262F] rounded-full flex items-center justify-center mb-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <p className="text-gray-400 text-sm">هیچ تراکنشی یافت نشد</p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className="md:hidden space-y-4 overflow-x-auto w-full relative z-10">
            {transactions.length > 0 ? (
              transactions.map((transaction) => {
                const item = transformTransactionToOrder(transaction);
                return (
                <div
                  key={transaction.id}
                  className="bg-gradient-to-br from-[#1C1E24] to-[#23262F] p-4 rounded-xl flex flex-col gap-y-4 w-full shadow-md border border-[#353945]/30 hover:border-[#353945]/50 transition-all duration-300 relative overflow-hidden"
                >
                  {/* Background pattern */}
                  <div className="absolute inset-0 bg-[radial-gradient(#353945_1px,transparent_1px)] [background-size:12px_12px] opacity-20"></div>

                  <div className="flex justify-between items-center relative z-10">
                    <span className="text-[#B1B5C3] text-xs font-medium">Date/Time:</span>
                    <span className="text-xs font-medium">{item.time}</span>
                  </div>

                  <div className="flex justify-between items-center relative z-10">
                    <span className="text-[#B1B5C3] text-xs font-medium">Pair:</span>
                    <div className="text-right flex items-center">
                      <div className="bg-[#353945]/50 p-1 rounded-full mr-2">
                        <Image
                          className="w-4 h-4"
                          src={`https://api.exchangim.com/storage/${item.coin_icon}`}
                          height={16}
                          width={16}
                          alt={item.coin_icon}
                          unoptimized
                          onError={(e) => {
                            console.error('Image failed to load:', `https://api.exchangim.com/storage/${item.coin_icon}`);
                            e.currentTarget.src = "/images/coin.png";
                          }}
                        />
                      </div>
                      <div>
                        <p className="text-sm font-medium bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">{item.currency}</p>
                        <p className="text-[10px] text-[#B1B5C3] font-light">
                          {item.currencyName}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center relative z-10">
                    <span className="text-[#B1B5C3] text-xs font-medium">Type:</span>
                    <span className={`${item.typeColor} text-xs font-medium px-2 py-1 rounded-full bg-[#23262F]/50`}>
                      {item.type}
                    </span>
                  </div>
                  <div className="flex justify-between items-center relative z-10">
                    <span className="text-[#B1B5C3] text-xs font-medium">Total Price:</span>
                    <span className="text-xs font-medium">{item.totalPrice}</span>
                  </div>
                  <div className="flex justify-between items-center relative z-10">
                    <span className="text-[#B1B5C3] text-xs font-medium">Amount:</span>
                    <span className="text-xs font-medium">{item.amount}</span>
                  </div>
                  <div className="flex justify-between items-center relative z-10">
                    <span className="text-[#B1B5C3] text-xs font-medium">Status:</span>
                    <span
                      className={`${item.statusBg} text-[#C5EFD8] p-1.5 rounded-lg text-xs font-medium shadow-sm`}
                    >
                      {item.status}
                    </span>
                  </div>
                  <div className="flex justify-center mt-3 relative z-10">
                    <Link
                      href={`/dashboard/transaction/${transaction.id}`}
                      className="bg-gradient-to-r from-[#23262F] to-[#2A2D38] hover:from-[#2A2D38] hover:to-[#353945] px-4 py-2 rounded-lg text-sm transition-colors border border-[#353945]/50 hover:border-[#353945]/80 inline-flex items-center justify-center w-full"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1.5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      مشاهده جزئیات تراکنش
                    </Link>
                  </div>
                </div>
              );
              })
            ) : (
              <div className="flex flex-col items-center justify-center py-10">
                <div className="w-16 h-16 bg-[#23262F] rounded-full flex items-center justify-center mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <p className="text-gray-400 text-sm">هیچ تراکنشی یافت نشد</p>
              </div>
            )}
          </div>

          {/* Pagination Controls */}
          {!loading && totalPages > 1 && (
            <div className="flex justify-center items-center mt-6 gap-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  currentPage === 1
                    ? 'bg-[#23262F]/50 text-gray-500 cursor-not-allowed'
                    : 'bg-[#23262F] hover:bg-[#2A2D38] text-white cursor-pointer'
                } transition-colors`}
              >
                قبلی
              </button>

              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                // Show pages around current page
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <button
                    key={pageNum}
                    onClick={() => setCurrentPage(pageNum)}
                    className={`w-8 h-8 flex items-center justify-center rounded-lg text-sm ${
                      currentPage === pageNum
                        ? 'bg-[#2FA766] text-white'
                        : 'bg-[#23262F] hover:bg-[#2A2D38] text-white'
                    } transition-colors`}
                  >
                    {pageNum}
                  </button>
                );
              })}

              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  currentPage === totalPages
                    ? 'bg-[#23262F]/50 text-gray-500 cursor-not-allowed'
                    : 'bg-[#23262F] hover:bg-[#2A2D38] text-white cursor-pointer'
                } transition-colors`}
              >
                بعدی
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

const UpgradeDetailsCard: React.FC<{ levelDetails: any }> = ({ levelDetails }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatCurrency = (amount: string) => {
    const num = parseFloat(amount);
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)} میلیون تومان`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)} هزار تومان`;
    }
    return `${num.toLocaleString()} تومان`;
  };

  const formatTime = (hours: number) => {
    const days = Math.floor(hours / 24);
    const remainingHours = Math.floor(hours % 24);
    return `${days} روز و ${remainingHours} ساعت`;
  };

  if (!levelDetails?.conditions?.details) return null;

  const details = levelDetails.conditions.details;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mt-3 space-y-2"
    >
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-2 bg-[#23262F]/50 rounded-lg hover:bg-[#23262F]/70 transition-all duration-300"
      >
        <span className="text-xs text-gray-300">جزئیات شرایط ارتقا</span>
        <motion.div
          animate={{ rotate: isExpanded ? 180 : 0 }}
          transition={{ duration: 0.3 }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </motion.div>
      </button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-2 overflow-hidden"
          >
            {/* Purchase Requirement */}
            {details.total_purchases && (
              <div className="p-3 bg-[#1A1D1F]/50 rounded-lg border border-[#353945]/30">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-[10px] text-gray-300">حداقل خرید کل</span>
                  <span className={`text-[10px] font-medium ${details.total_purchases.met ? 'text-green-400' : 'text-yellow-400'}`}>
                    {details.total_purchases.met ? '✅ تکمیل شده' : '⏳ در حال پیشرفت'}
                  </span>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between text-[9px] text-gray-400">
                    <span>مقدار فعلی:</span>
                    <span className="text-green-400">{formatCurrency(details.total_purchases.current)}</span>
                  </div>
                  <div className="flex justify-between text-[9px] text-gray-400">
                    <span>مقدار مورد نیاز:</span>
                    <span className="text-blue-400">{formatCurrency(details.total_purchases.required)}</span>
                  </div>
                  <div className="w-full bg-[#23262F] rounded-full h-1.5 mt-2">
                    <motion.div
                      className="bg-gradient-to-r from-green-500 to-green-400 h-1.5 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ 
                        width: `${Math.min(100, (parseFloat(details.total_purchases.current) / parseFloat(details.total_purchases.required)) * 100)}%` 
                      }}
                      transition={{ duration: 1, delay: 0.5 }}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Time Requirement */}
            {details.time_requirement && (
              <div className="p-3 bg-[#1A1D1F]/50 rounded-lg border border-[#353945]/30">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-[10px] text-gray-300">زمان مورد نیاز</span>
                  <span className={`text-[10px] font-medium ${details.time_requirement.met ? 'text-green-400' : 'text-yellow-400'}`}>
                    {details.time_requirement.met ? '✅ تکمیل شده' : '⏳ در حال انتظار'}
                  </span>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between text-[9px] text-gray-400">
                    <span>زمان سپری شده:</span>
                    <span className="text-green-400">{formatTime(details.time_requirement.current_hours)}</span>
                  </div>
                  <div className="flex justify-between text-[9px] text-gray-400">
                    <span>زمان مورد نیاز:</span>
                    <span className="text-blue-400">{formatTime(details.time_requirement.required_hours)}</span>
                  </div>
                  <div className="w-full bg-[#23262F] rounded-full h-1.5 mt-2">
                    <motion.div
                      className="bg-gradient-to-r from-blue-500 to-blue-400 h-1.5 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ 
                        width: `${Math.min(100, (details.time_requirement.current_hours / details.time_requirement.required_hours) * 100)}%` 
                      }}
                      transition={{ duration: 1, delay: 0.7 }}
                    />
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default Page;
