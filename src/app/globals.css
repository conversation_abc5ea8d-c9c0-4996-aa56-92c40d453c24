@import "tailwindcss";

:root {
  --font-vazirmatn: var(--font-vazirmatn);
  --font-outfit: var(--font-outfit);
}

.font-vazir {
  font-family: var(--font-vazirmatn);
}

.font-outfit {
  font-family: var(--font-outfit);
}

* {
  font-family: Vazirmatn;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.highcharts-label,
.highcharts-scrollbar {
  display: none;
}

/* Safe area for mobile devices */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
