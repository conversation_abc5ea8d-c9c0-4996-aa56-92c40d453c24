@import "tailwindcss";

:root {
  --font-vazirmatn: var(--font-vazirmatn);
  --font-outfit: var(--font-outfit);
}

.font-vazir {
  font-family: var(--font-vazirmatn);
}

.font-outfit {
  font-family: var(--font-outfit);
}

* {
  font-family: Vazirmatn;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.highcharts-label,
.highcharts-scrollbar {
  display: none;
}

/* Safe area for mobile devices */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Custom Scrollbar for Sidebar */
.sidebar-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(59,130,246,0.6) 0%, rgba(147,51,234,0.4) 100%);
  border-radius: 10px;
  border: 1px solid rgba(255,255,255,0.1);
}

.sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(59,130,246,0.8) 0%, rgba(147,51,234,0.6) 100%);
  box-shadow: 0 0 10px rgba(59,130,246,0.4);
}
